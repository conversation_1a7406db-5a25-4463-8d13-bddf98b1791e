---
import Layout from "../../layouts/Layout.astro";

// We'll keep this route but make it redirect to the new subcategory structure
export async function getStaticPaths() {
  // We don't need to generate any static paths here anymore
  // as all coloring pages will be under the subcategory structure
  return [];
}

// This is just a fallback in case someone uses an old URL format
// We'll redirect them to the homepage
const { category } = Astro.params;
---

<Layout title="Page moved" description="This page has been moved">
  <div class="container mx-auto px-4 py-8 text-center">
    <h1 class="text-3xl md:text-4xl font-bold mb-4">
      Page structure has changed
    </h1>
    <p class="text-lg mb-8">This page has moved to a new location.</p>
    <a href="/" class="btn btn-primary">Go to Homepage</a>
  </div>
</Layout>

<script>
  // Redirect to homepage after a short delay
  setTimeout(() => {
    window.location.href = "/";
  }, 2000);
</script>
