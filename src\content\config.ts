/**
 * Configuration file for Astro content collections.
 *
 * IMPORTANT DESIGN PHILOSOPHY:
 * In this application, folder and file names serve as the primary identifiers
 * for categories and coloring pages. There is no explicit "name" field in either schema
 * because the folder/file name is the definitive source of truth for:
 * - The entity name/title (when formatted with getDisplayName)
 * - The slug used in URLs
 * - The identifier for referencing related items
 *
 * This design choice keeps the system simple and ensures consistency across
 * the application - the name of something is derived from its file location
 * rather than duplicated in a content field.
 */
import { defineCollection, z } from "astro:content";

/**
 * Category collection schema.
 * Note: The category name/identifier comes from the filename itself,
 * not from a field in this schema. For example, a file at "categories/animals/wild.md"
 * would represent the "wild" subcategory under the "animals" category.
 *
 * The parent-child relationship is now inferred from the file structure:
 * - Files directly in src/content/categories/ are main categories
 * - Files in src/content/categories/[category]/ are subcategories of [category]
 */
const categories = defineCollection({
  schema: z.object({
    name: z.string().optional(), // Optional display name, overrides slug formatting
    description: z.string(),
    relatedCategories: z.array(z.string()).optional(), // References to other category slugs
    featured: z.boolean().optional(), // Whether to feature this category on the homepage
  }),
});

/**
 * Coloring Pages collection schema.
 * Note: The coloring page name/identifier comes from the filename itself,
 * not from a field in this schema. For example, a file at "coloring-pages/animals/wild/lion.md"
 * would represent the "lion" coloring page in the "wild" subcategory of "animals".
 *
 * The category and subcategory are now inferred from the file structure:
 * - Files at src/content/coloring-pages/[category]/[subcategory]/[name].md
 *   belong to [category] and [subcategory]
 */
const coloringPages = defineCollection({
  schema: z.object({
    name: z.string().optional(), // Optional display name, overrides slug formatting
    description: z.string(),
    difficulty: z.enum(["easy", "medium", "hard", "expert"]),
    featured: z.boolean().optional(), // Whether to feature this coloring page on the homepage
  }),
});

export const collections = {
  categories,
  "coloring-pages": coloringPages,
};
