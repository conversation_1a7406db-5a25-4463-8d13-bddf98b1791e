---
import CategoryCard from "../common/CategoryCard.astro";
import {
  getAllCategories,
  getMainCategories,
  getDisplayName,
  getCategoryThumbnailPath,
} from "../../utils/collections";

interface Props {
  limit?: number;
}

const { limit = 4 } = Astro.props;

const allCategories = await getAllCategories();
const mainCategories = await getMainCategories();

// Get all subcategories (categories that aren't main categories)
const subcategories = allCategories.filter(
  (category) => !mainCategories.some((mc) => mc.slug === category.slug)
);

// Get featured subcategories first
const featuredSubcategories = subcategories.filter(
  (category) => category.data.featured === true
);

// If we don't have enough featured subcategories, add non-featured ones
const nonFeaturedSubcategories = subcategories
  .filter((category) => !category.data.featured)
  .slice(0, limit - featuredSubcategories.length);

// Combine and limit to max subcategories
const displaySubcategories = [
  ...featuredSubcategories,
  ...nonFeaturedSubcategories,
].slice(0, limit);
---

<section class="py-12 bg-base-200">
  <div class="container mx-auto px-4">
    <div class="text-center mb-10">
      <h2 class="text-3xl md:text-4xl font-bold mb-2">
        Featured Subcategories
      </h2>
      <p class="text-neutral-600 max-w-2xl mx-auto">
        Explore our specially curated subcategories of coloring pages. Find more
        specific themes to match your interests!
      </p>
    </div>

    <div
      class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
      {
        displaySubcategories.map((category, idx) => (
          <div
            class="animate-fade-in"
            style={`animation-delay: ${idx * 100}ms`}>
            <CategoryCard
              id={category.slug}
              name={getDisplayName(category)}
              description={category.data.description}
              thumbnail={getCategoryThumbnailPath(category)}
              slug={category.slug}
            />
          </div>
        ))
      }
    </div>

    <div class="text-center mt-10">
      <a href="/categories/" class="btn btn-primary">
        View All Categories
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 ml-1"
          viewBox="0 0 20 20"
          fill="currentColor">
          <path
            fill-rule="evenodd"
            d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
            clip-rule="evenodd"></path>
        </svg>
      </a>
    </div>
  </div>
</section>
