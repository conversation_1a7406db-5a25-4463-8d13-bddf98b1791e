/**
 * Interactive coloring canvas functionality
 * This script is lazy-loaded when the page loads
 */

// Color palettes definition - moved from the Astro component
const firstRowColors = [
  "#FF0000",
  "#CC0000",
  "#FFFF00",
  "#FFCC00",
  "#00FF00",
  "#00CC00",
  "#00FFFF",
  "#00CCCC",
  "#0000FF",
  "#0000CC",
  "#FF00FF",
  "#CC00CC",
];
const secondRowColors = ["#FFFFFF", "#000000"];

// Function to generate HTML markup for the coloring canvas
function generateColoringHTML(id, title, imageUrl) {
  return `
    <div class="coloring-page-container-interactive">
      <!-- Interactive Coloring Mode Structure -->
      <div id="interactiveMode" class="interactive-mode viewport-container">
        <div class="coloring-canvas-container">
          <div class="canvas-wrapper">
            <!-- Canvas element where drawing happens -->
            <canvas id="coloringCanvas"></canvas>
            <!-- Hidden source image used by the JS to initialize the canvas -->
            <img
              id="sourceImage"
              src="${imageUrl}"
              alt="${title}"
              class="hidden"
              crossorigin="anonymous"
            />
          </div>
        </div>

        <!-- Toolbar container directly below the canvas -->
        <div class="toolbar-container">
          <!-- Color Options -->
          ${firstRowColors
            .map(
              (color, index) => `
            <button
              class="color-option ${index === 0 ? "active" : ""}"
              data-color="${color}"
              style="background-color: ${color};"
              aria-label="Select color ${color}"
              title="Color ${color}"
            ></button>
          `
            )
            .join("")}
          
          ${secondRowColors
            .map(
              (color) => `
            <button
              class="color-option"
              data-color="${color}"
              style="background-color: ${color};"
              aria-label="Select color ${color}"
              title="Color ${color}"
            ></button>
          `
            )
            .join("")}

          <!-- Custom Color Pickers -->
          <div class="custom-color-picker" id="customColorPicker1Container">
            <div class="custom-color-display" id="customColorDisplay1"></div>
            <div class="color-wheel-overlay"></div>
            <input
              type="color"
              id="colorPicker1"
              value="#CCCCCC"
              aria-label="Custom color picker 1"
              class="custom-color-input"
            />
          </div>
          <div class="custom-color-picker" id="customColorPicker2Container">
            <div class="custom-color-display" id="customColorDisplay2"></div>
            <div class="color-wheel-overlay"></div>
            <input
              type="color"
              id="colorPicker2"
              value="#888888"
              aria-label="Custom color picker 2"
              class="custom-color-input"
            />
          </div>

          <!-- Tools: Eraser, Brush Sizes, Fill, Undo, Redo -->
          <button class="tool-button" id="eraserBtn" title="Eraser">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="tool-icon"
              ><path
                d="m7 21-4.3-4.3c-1-1-1-2.5 0-3.4l9.6-9.6c1-1 2.5-1 3.4 0l5.6 5.6c1 1 1 2.5 0 3.4L13 21"
              ></path><path d="M22 21H7"></path><path d="m5 11 9 9"></path></svg
            >
          </button>
          <button
            class="tool-button brush-size"
            id="extraSmallBrush"
            data-size="extraSmall"
            aria-label="Extra small brush"
            title="Extra Small Brush">
            <div class="brush-preview extra-small-brush"></div>
          </button>
          <button
            class="tool-button brush-size active"
            id="smallBrush"
            data-size="small"
            aria-label="Small brush"
            title="Small Brush">
            <div class="brush-preview small-brush"></div>
          </button>
          <button
            class="tool-button brush-size"
            id="mediumBrush"
            data-size="medium"
            aria-label="Medium brush"
            title="Medium Brush">
            <div class="brush-preview medium-brush"></div>
          </button>
          <button
            class="tool-button brush-size"
            id="largeBrush"
            data-size="large"
            aria-label="Large brush"
            title="Large Brush">
            <div class="brush-preview large-brush"></div>
          </button>
          <button class="tool-button" id="fillBtn" title="Fill mode">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="tool-icon"
              ><path
                d="m19 11-8-8-8.6 8.6a2 2 0 0 0 0 2.8l5.2 5.2c.8.8 2 .8 2.8 0L19 11Z"
              ></path><path d="m5 2 5 5"></path><path d="M2 13h15"></path><path
                d="M22 20a2 2 0 1 1-4 0c0-1.6 1.7-2.4 2-4 .3 1.6 2 2.4 2 4Z"
              ></path></svg
            >
          </button>
          <button class="tool-button" id="undoBtn" title="Undo" disabled>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="tool-icon"
              ><path d="M3 7v6h6"></path><path
                d="M21 17a9 9 0 0 0-9-9 9 9 0 0 0-6 2.3L3 13"></path></svg
            >
          </button>
          <button class="tool-button" id="redoBtn" title="Redo" disabled>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="tool-icon"
              ><path d="M21 7v6h-6"></path><path
                d="M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7"></path></svg
            >
          </button>
        </div>
      </div>
    </div>
  `;
}

// Function to inject CSS styles
function injectStyles() {
  const styleElement = document.createElement("style");
  styleElement.textContent = `
    /* Styles for the coloring canvas component */
    .coloring-page-container-interactive {
      width: 100%;
      max-width: 100%; 
      margin: 0 auto;
      display: flex;
      flex-direction: column;
      align-items: center;
      overflow: hidden;
    }

    .interactive-mode {
      width: 100%;
      display: flex;
      flex-direction: column;
      overflow: visible;
      gap: 1rem;
      align-items: center;
    }

    .viewport-container {
      display: flex;
      flex-direction: column;
      width: 100%;
      align-items: center;
    }

    .coloring-canvas-container {
      display: flex;
      flex-direction: column;
      width: 100%;
      border-radius: 0.5rem;
      overflow: hidden;
      box-shadow:
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06);
      background: white;
      margin: 0 auto;
      align-items: center;
      border: 1px solid rgba(0, 0, 0, 0.1);
    }

    .canvas-wrapper {
      position: relative;
      width: 100%;
      height: auto;
      overflow: hidden;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #f0f0f0;
    }

    #coloringCanvas {
      display: block;
      width: 100%;
      height: 100%;
      touch-action: none;
      cursor: crosshair;
      object-fit: contain;
      background-color: transparent;
    }

    .toolbar-container {
      display: grid;
      grid-template-columns: repeat(12, 32px);
      gap: 6px;
      justify-content: center;
      align-items: center;
      background-color: #f0f4f8;
      border-radius: 0.5rem;
      padding: 0.75rem;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      max-width: calc(100% - 2rem);
      width: max-content;
      margin: 1rem auto;
      border: 1px solid #d1d5db;
    }

    .color-option {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      border: 2px solid #e5e7eb;
      cursor: pointer;
      transition: all 0.2s ease;
      padding: 0;
      margin: 0;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .color-option:hover {
      transform: scale(1.1);
      box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
      z-index: 1;
    }

    .color-option.active {
      border-color: #3b82f6;
      transform: scale(1.1);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
      z-index: 2;
    }

    .custom-color-picker {
      position: relative;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }

    .custom-color-display {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      border: 2px solid #e5e7eb;
      background-color: var(--custom-color, #ccc);
      transition: all 0.2s ease;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .color-wheel-overlay {
      position: absolute;
      bottom: -1px;
      right: -1px;
      width: 15px;
      height: 15px;
      border-radius: 50%;
      background: conic-gradient(red, yellow, lime, aqua, blue, magenta, red);
      border: 1px solid #fff;
      pointer-events: none;
      z-index: 3;
      box-shadow: 0 0 2px rgba(0, 0, 0, 0.3);
    }

    .custom-color-input {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0;
      pointer-events: none;
      z-index: 2;
      cursor: pointer;
    }

    .custom-color-picker:hover .custom-color-display {
      transform: scale(1.1);
      box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
      z-index: 1;
    }

    .custom-color-picker.active .custom-color-display {
      border-color: #3b82f6;
      transform: scale(1.1);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
      z-index: 2;
    }

    .tool-button {
      width: 32px;
      height: 32px;
      border-radius: 0.25rem;
      border: 1px solid #d1d5db;
      background-color: #ffffff;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      padding: 3px;
      margin: 0;
      color: #374151;
    }

    .tool-icon {
      width: 20px;
      height: 20px;
    }

    .tool-button:hover {
      background-color: #f9fafb;
      border-color: #9ca3af;
      color: #1f2937;
    }

    .tool-button.active {
      background-color: #e5e7eb;
      border-color: #6b7280;
      color: #111827;
      box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .tool-button:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      background-color: #f3f4f6;
    }

    .tool-button:disabled:hover {
      border-color: #d1d5db;
      color: #374151;
    }

    .brush-size {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .brush-preview {
      border-radius: 50%;
      background-color: #000000;
      transition: all 0.2s ease;
    }

    .brush-size.active .brush-preview {
      background-color: #111827;
    }

    .extra-small-brush { width: 6px; height: 6px; }
    .small-brush { width: 10px; height: 10px; }
    .medium-brush { width: 16px; height: 16px; }
    .large-brush { width: 22px; height: 22px; }

    .hidden { display: none; }

    /* Responsive adjustments */
    @media (min-width: 1200px) {
      .toolbar-container {
        grid-template-columns: repeat(12, 40px);
        gap: 8px;
        padding: 1rem;
      }

      .color-option, .custom-color-picker, .tool-button {
        width: 40px;
        height: 40px;
      }
      .tool-icon {
        width: 24px;
        height: 24px;
      }
      .color-wheel-overlay {
        width: 18px;
        height: 18px;
        bottom: 0px;
        right: 0px;
      }
      .extra-small-brush { width: 8px; height: 8px; }
      .small-brush { width: 14px; height: 14px; }
      .medium-brush { width: 20px; height: 20px; }
      .large-brush { width: 28px; height: 28px; }
    }

    @media (max-width: 767px) {
      .toolbar-container {
        grid-template-columns: repeat(auto-fit, minmax(32px, 1fr));
        max-width: 100%;
        width: 100%;
        padding: 0.5rem;
        gap: 4px;
      }
      .color-option, .custom-color-picker, .tool-button {
        width: 36px;
        height: 36px;
      }
      .tool-icon {
        width: 20px;
        height: 20px;
      }
      .color-wheel-overlay {
        width: 16px;
        height: 16px;
        bottom: 0px;
        right: 0px;
      }
    }
    
    @media (max-width: 480px) {
      .toolbar-container {
        grid-template-columns: repeat(auto-fit, minmax(30px, 1fr));
        gap: 3px;
      }
      .color-option, .custom-color-picker, .tool-button {
        width: 32px;
        height: 32px;
      }
      .tool-icon {
        width: 18px;
        height: 18px;
      }
      .color-wheel-overlay {
        width: 14px;
        height: 14px;
      }
    }
  `;
  document.head.appendChild(styleElement);
}

// Main function to setup the coloring canvas - called when "Color Now" is clicked
export function setupColoringCanvas(containerId, id, title, imageUrl) {
  // Get the container element
  const container = document.getElementById(containerId);
  if (!container) return false;

  // Generate and inject HTML markup
  container.innerHTML = generateColoringHTML(id, title, imageUrl);

  // Inject CSS styles
  injectStyles();

  // Initialize interactive canvas functionality
  initColoringCanvas();

  return true; // Success
}

export function initColoringCanvas() {
  // Drawing state variables
  let isDrawing = false;
  let isErasing = false;
  let isFillMode = false;
  let currentColor = "#FF0000"; // Default color, will be updated
  let brushSize = 6; // Default brush size, will be updated
  let originalImageData = null;

  // Scaling and resolution variables
  let scaleX = 1;
  let scaleY = 1;
  let fixedWidth = 800; // Default fallback
  let fixedHeight = 600; // Default fallback

  // Get references to DOM elements
  const canvas = document.getElementById("coloringCanvas");
  const ctx = canvas?.getContext("2d", { willReadFrequently: true }); // Added willReadFrequently for performance
  const sourceImage = document.getElementById("sourceImage");
  const colorPicker1 = document.getElementById("colorPicker1");
  const colorPicker2 = document.getElementById("colorPicker2");
  const customColorPicker1Container = document.getElementById(
    "customColorPicker1Container"
  );
  const customColorPicker2Container = document.getElementById(
    "customColorPicker2Container"
  );
  const customColorDisplay1 = document.getElementById("customColorDisplay1");
  const customColorDisplay2 = document.getElementById("customColorDisplay2");
  const colorOptions = document.querySelectorAll(".color-option");
  const eraserBtn = document.getElementById("eraserBtn");
  const fillBtn = document.getElementById("fillBtn");
  const undoBtn = document.getElementById("undoBtn");
  const redoBtn = document.getElementById("redoBtn");
  const brushSizes = document.querySelectorAll(".brush-size");
  const downloadBtn = document.getElementById("downloadBtn");
  const printBtn = document.getElementById("printBtn");
  const shareBtn = document.getElementById("shareBtn");

  // Memory variables for tool state
  let lastSelectedColor = currentColor; // Remember last color when using eraser
  let lastSelectedBrushSize = brushSize; // Remember last brush size when using fill
  let lastActiveBrushButton = null; // Remember last active brush button
  let lastActiveColorOption = null; // Remember last active color option
  let lastActiveColorPickerIndex = null; // Remember last active color picker

  // History for undo/redo functionality
  const maxHistoryStates = 20;
  let history = [];
  let historyIndex = -1;

  // Custom color picker state
  let customColor1 = colorPicker1?.value || "#CCCCCC"; // Updated default
  let customColor2 = colorPicker2?.value || "#888888"; // Updated default

  // Long press state for mobile
  let longPressTimer = null;
  const longPressDuration = 500; // ms

  // Save current state to history
  function saveToHistory() {
    if (!canvas || !ctx) return;

    // Get current canvas state
    const currentState = ctx.getImageData(0, 0, canvas.width, canvas.height);

    // If we're not at the end of the history, remove future states
    if (historyIndex < history.length - 1) {
      history = history.slice(0, historyIndex + 1);
    }

    // Add current state to history
    history.push(currentState);

    // Limit history size
    if (history.length > maxHistoryStates) {
      history.shift();
    }

    // Update history index
    historyIndex = history.length - 1;

    // Enable/disable undo/redo buttons
    updateUndoRedoButtons();
  }

  // Update undo/redo buttons state
  function updateUndoRedoButtons() {
    if (undoBtn && redoBtn) {
      undoBtn.disabled = historyIndex < 1;
      redoBtn.disabled = historyIndex >= history.length - 1;

      // Visual feedback
      undoBtn.classList.toggle("disabled", historyIndex < 1);
      redoBtn.classList.toggle("disabled", historyIndex >= history.length - 1);
    }
  }

  // Undo function
  function undo() {
    if (!canvas || !ctx || historyIndex < 1) return;

    historyIndex--;
    ctx.putImageData(history[historyIndex], 0, 0);
    updateUndoRedoButtons();
  }

  // Redo function
  function redo() {
    if (!canvas || !ctx || historyIndex >= history.length - 1) return;

    historyIndex++;
    ctx.putImageData(history[historyIndex], 0, 0);
    updateUndoRedoButtons();
  }

  // Fill function (flood fill algorithm) - Accepts coordinates in fixed canvas space
  function floodFill(startX, startY, fillColor) {
    if (!canvas || !ctx) return;

    // Ensure coordinates are integers
    startX = Math.floor(startX);
    startY = Math.floor(startY);

    // Get canvas image data (uses fixedWidth, fixedHeight)
    const imageData = ctx.getImageData(0, 0, fixedWidth, fixedHeight);
    const data = imageData.data;
    const width = fixedWidth; // Use fixedWidth
    const height = fixedHeight; // Use fixedHeight

    // Convert fill color from hex or rgba to RGBA array
    let fillColorRgba;

    if (fillColor.startsWith("rgba")) {
      // Parse rgba format
      const rgba = fillColor.match(
        /rgba\((\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)\)/
      );
      if (rgba) {
        fillColorRgba = [
          parseInt(rgba[1]),
          parseInt(rgba[2]),
          parseInt(rgba[3]),
          Math.round(parseFloat(rgba[4]) * 255),
        ];
      } else {
        // Default to transparent if parsing fails
        fillColorRgba = [0, 0, 0, 0];
      }
    } else {
      // Parse hex format
      const hexToRgb = (hex) => {
        const r = parseInt(hex.slice(1, 3), 16);
        const g = parseInt(hex.slice(3, 5), 16);
        const b = parseInt(hex.slice(5, 7), 16);
        return [r, g, b, 255]; // Full opacity
      };

      fillColorRgba = hexToRgb(fillColor);
    }

    // Get the color of the starting pixel
    const startPos = (startY * width + startX) * 4;
    const startColor = [
      data[startPos],
      data[startPos + 1],
      data[startPos + 2],
      data[startPos + 3],
    ];

    // If target color is the same as fill color, do nothing
    if (
      startColor[0] === fillColorRgba[0] &&
      startColor[1] === fillColorRgba[1] &&
      startColor[2] === fillColorRgba[2] &&
      startColor[3] === fillColorRgba[3]
    ) {
      return;
    }

    // Queue for flood fill
    const pixelsToCheck = [];
    pixelsToCheck.push([startX, startY]);

    // Color match threshold (for anti-aliased edges)
    const colorThreshold = 30;

    // Check if colors are similar
    const colorMatch = (color1, color2) => {
      return (
        Math.abs(color1[0] - color2[0]) < colorThreshold &&
        Math.abs(color1[1] - color2[1]) < colorThreshold &&
        Math.abs(color1[2] - color2[2]) < colorThreshold &&
        Math.abs(color1[3] - color2[3]) < colorThreshold
      );
    };

    // Process pixels
    while (pixelsToCheck.length > 0) {
      const [x, y] = pixelsToCheck.pop();

      // Skip if outside canvas
      if (x < 0 || y < 0 || x >= width || y >= height) {
        continue;
      }

      // Get current pixel position
      const pos = (y * width + x) * 4;

      // Get current color
      const currentColor = [
        data[pos],
        data[pos + 1],
        data[pos + 2],
        data[pos + 3],
      ];

      // Skip if not matching the start color
      if (!colorMatch(currentColor, startColor)) {
        continue;
      }

      // Fill the pixel
      data[pos] = fillColorRgba[0];
      data[pos + 1] = fillColorRgba[1];
      data[pos + 2] = fillColorRgba[2];
      data[pos + 3] = fillColorRgba[3];

      // Add adjacent pixels to check
      pixelsToCheck.push([x + 1, y]);
      pixelsToCheck.push([x - 1, y]);
      pixelsToCheck.push([x, y + 1]);
      pixelsToCheck.push([x, y - 1]);
    }

    // Update canvas with filled area
    ctx.putImageData(imageData, 0, 0);

    // Save state for undo
    saveToHistory();
  }

  // Set up the canvas when the image loads
  function setupCanvas() {
    if (!canvas || !ctx || !sourceImage) return;

    const canvasWrapper = canvas.parentElement;
    if (!canvasWrapper) return;

    // Get natural image dimensions
    const imgNaturalWidth = sourceImage.naturalWidth;
    const imgNaturalHeight = sourceImage.naturalHeight;

    if (imgNaturalWidth === 0 || imgNaturalHeight === 0) {
      setTimeout(setupCanvas, 100);
      return;
    }

    // Determine fixed high resolution (e.g., natural size, maybe capped)
    const maxDim = 3000; // Max dimension cap
    fixedWidth = Math.min(imgNaturalWidth, maxDim);
    fixedHeight = Math.min(imgNaturalHeight, maxDim);
    // Adjust the other dimension if one was capped to maintain aspect ratio
    if (imgNaturalWidth > maxDim && imgNaturalWidth >= imgNaturalHeight) {
      fixedHeight = Math.round((maxDim / imgNaturalWidth) * imgNaturalHeight);
    } else if (
      imgNaturalHeight > maxDim &&
      imgNaturalHeight > imgNaturalWidth
    ) {
      fixedWidth = Math.round((maxDim / imgNaturalHeight) * imgNaturalWidth);
    }
    // Ensure dimensions are at least 1px
    fixedWidth = Math.max(1, fixedWidth);
    fixedHeight = Math.max(1, fixedHeight);

    // --- Calculate display size (responsible for visual scaling) ---
    const coloring_canvas_container = canvasWrapper.closest(
      ".coloring-canvas-container"
    );
    // Reset container width temporarily to measure available space
    if (coloring_canvas_container) {
      coloring_canvas_container.style.width = "100%";
      coloring_canvas_container.style.maxWidth = "100%";
    }
    // Measure available space
    const containerWidth =
      coloring_canvas_container?.clientWidth ||
      canvasWrapper.parentElement?.clientWidth ||
      800;
    const toolbarHeight =
      document.querySelector(".toolbar-container")?.clientHeight || 100;
    const viewportHeight = window.innerHeight - toolbarHeight - 80; // 80px for margins/padding
    const imageAspectRatio = fixedWidth / fixedHeight; // Use fixed aspect ratio

    // Calculate display dimensions based on container and viewport
    let displayWidth = containerWidth;
    let displayHeight = displayWidth / imageAspectRatio;

    // If height exceeds viewport, constrain by height
    if (displayHeight > viewportHeight) {
      displayHeight = viewportHeight;
      displayWidth = displayHeight * imageAspectRatio;
    }

    // Ensure display dimensions are positive
    displayWidth = Math.max(1, displayWidth);
    displayHeight = Math.max(1, displayHeight);

    // Update the coloring canvas container width to match the calculated display width
    // This prevents the container from being wider than the canvas display size
    if (coloring_canvas_container) {
      coloring_canvas_container.style.width = `${displayWidth}px`;
    }

    // Update canvas wrapper dimensions (visual container for the canvas element)
    canvasWrapper.style.width = `${displayWidth}px`;
    canvasWrapper.style.height = `${displayHeight}px`;

    // --- Set canvas element size (fixed internal resolution) vs style size (CSS display) ---
    canvas.width = fixedWidth;
    canvas.height = fixedHeight;
    canvas.style.width = `${displayWidth}px`;
    canvas.style.height = `${displayHeight}px`;

    // Calculate scaling factors for converting display coords to canvas coords
    scaleX = fixedWidth / displayWidth;
    scaleY = fixedHeight / displayHeight;

    // --- Drawing --- //
    ctx.setTransform(1, 0, 0, 1, 0, 0); // Reset transform (important!)
    // No DPR scaling needed, fixed resolution handles quality
    // ctx.scale(dpr, dpr); // REMOVED

    // Disable image smoothing when drawing the initial image for sharpness
    ctx.imageSmoothingEnabled = false;
    ctx.clearRect(0, 0, fixedWidth, fixedHeight); // Clear before drawing
    ctx.drawImage(sourceImage, 0, 0, fixedWidth, fixedHeight); // Draw image at full fixed resolution
    ctx.imageSmoothingEnabled = true; // Re-enable for subsequent drawing operations if needed (though line drawing doesn't use it)

    // Store the original image data for reset/erase functionality (at fixed resolution)
    originalImageData = ctx.getImageData(0, 0, fixedWidth, fixedHeight);

    // Initialize history with original state (at fixed resolution)
    history = [originalImageData];
    historyIndex = 0;

    // Update undo/redo buttons
    updateUndoRedoButtons();
  }

  // Drawing functions
  function startDrawing(e) {
    if (!canvas || !ctx) return;

    const rect = canvas.getBoundingClientRect();
    let clientX, clientY;

    // Handle both mouse and touch events
    if (e.type.startsWith("touch")) {
      if (e.touches.length > 0) {
        clientX = e.touches[0].clientX;
        clientY = e.touches[0].clientY;
      } else {
        return;
      }
    } else {
      clientX = e.clientX;
      clientY = e.clientY;
    }

    // Convert window/display coordinates to fixed canvas coordinates
    const canvasX = (clientX - rect.left) * scaleX;
    const canvasY = (clientY - rect.top) * scaleY;

    // If in fill mode, perform flood fill using canvas coordinates
    if (isFillMode) {
      floodFill(canvasX, canvasY, isErasing ? "rgba(0,0,0,0)" : currentColor);
      return;
    }

    // Otherwise start drawing
    isDrawing = true;

    // Begin path and move to the starting point (using canvas coordinates)
    ctx.beginPath();
    ctx.moveTo(canvasX, canvasY);

    // Note: Saving history is now done in stopDrawing
  }

  function stopDrawing() {
    if (isDrawing) {
      isDrawing = false;
      // ctx?.beginPath(); // Path is already managed

      // Save the state after drawing is complete (at fixed resolution)
      saveToHistory();
    }
  }

  function draw(e) {
    if (!isDrawing || !canvas || !ctx) return;

    const rect = canvas.getBoundingClientRect();
    let clientX, clientY;

    // Handle both mouse and touch events
    if (e.type.startsWith("touch")) {
      if (e.touches.length > 0) {
        clientX = e.touches[0].clientX;
        clientY = e.touches[0].clientY;
      } else {
        return;
      }
    } else {
      clientX = e.clientX;
      clientY = e.clientY;
    }

    // Convert window/display coordinates to fixed canvas coordinates
    const canvasX = (clientX - rect.left) * scaleX;
    const canvasY = (clientY - rect.top) * scaleY;

    // Set drawing styles (lineWidth is in fixed resolution pixels)
    ctx.lineWidth = brushSize;
    ctx.lineCap = "round";
    ctx.lineJoin = "round";

    if (isErasing) {
      // Use composite operation to erase (reveals transparency on this layer)
      ctx.globalCompositeOperation = "destination-out"; // Erases on the current layer
      ctx.strokeStyle = "rgba(0,0,0,1)";
    } else {
      ctx.globalCompositeOperation = "source-over"; // Default drawing mode
      ctx.strokeStyle = currentColor;
    }

    // Continue the path and stroke (using canvas coordinates)
    ctx.lineTo(canvasX, canvasY);
    ctx.stroke();

    // Important: To ensure smooth drawing, start the next segment from the current point
    ctx.beginPath();
    ctx.moveTo(canvasX, canvasY);
  }

  // Handle window resize events - Simplified!
  function handleResize() {
    if (!canvas || !ctx || !sourceImage) return; // Need sourceImage for aspect ratio

    // --- Recalculate display size --- //
    const canvasWrapper = canvas.parentElement;
    if (!canvasWrapper) return;

    const coloring_canvas_container = canvasWrapper.closest(
      ".coloring-canvas-container"
    );

    // Allow container to measure full width
    if (coloring_canvas_container) {
      coloring_canvas_container.style.width = "100%";
      coloring_canvas_container.style.maxWidth = "100%";
    }

    const containerWidth =
      coloring_canvas_container?.clientWidth ||
      canvasWrapper.parentElement?.clientWidth ||
      800;
    const toolbarHeight =
      document.querySelector(".toolbar-container")?.clientHeight || 100;
    const viewportHeight = window.innerHeight - toolbarHeight - 80;
    const imageAspectRatio = fixedWidth / fixedHeight; // Use fixed aspect ratio

    let displayWidth = containerWidth;
    let displayHeight = displayWidth / imageAspectRatio;

    if (displayHeight > viewportHeight) {
      displayHeight = viewportHeight;
      displayWidth = displayHeight * imageAspectRatio;
    }

    // Ensure positive dimensions
    displayWidth = Math.max(1, displayWidth);
    displayHeight = Math.max(1, displayHeight);

    // Update container width
    if (coloring_canvas_container) {
      coloring_canvas_container.style.width = `${displayWidth}px`;
    }
    // Update wrapper size
    canvasWrapper.style.width = `${displayWidth}px`;
    canvasWrapper.style.height = `${displayHeight}px`;

    // --- Update CSS style dimensions for the canvas element --- //
    canvas.style.width = `${displayWidth}px`;
    canvas.style.height = `${displayHeight}px`;

    // --- Update scaling factors --- //
    scaleX = fixedWidth / displayWidth;
    scaleY = fixedHeight / displayHeight;

    // --- No need to redraw canvas content --- //
    // The browser handles scaling the canvas element via CSS
  }

  // Export canvas content
  function exportCanvas(action) {
    if (!canvas) return;

    // Create a temporary canvas to draw the potentially downscaled version for preview/print if needed
    // Or simply export the high-resolution `canvas` directly.
    // Exporting high-res `canvas` is usually preferred for quality.
    const exportDataURL = canvas.toDataURL("image/png");

    switch (action) {
      case "download":
        const link = document.createElement("a");
        link.download = "colored-image.png";
        link.href = exportDataURL; // Use the high-res version
        link.click();
        break;

      case "print":
        const printWindow = window.open("", "_blank");
        if (!printWindow) return;
        printWindow.document.write(`
          <html><head><title>Print Coloring Page</title>
            <style>
              body { margin: 0; }
              img { width: 100%; height: auto; image-rendering: pixelated; /* Optional: force sharp pixels if needed */ }
              @media print {
                @page { size: auto; margin: 0.5cm; }
                body { margin: 0; }
                img { max-width: 100%; height: auto; }
              }
            </style>
          </head><body>
            <img src="${exportDataURL}"> 
          </body></html>
        `); // Use high-res
        printWindow.document.close();
        printWindow.focus();
        // Delay print slightly to allow image rendering
        setTimeout(() => {
          printWindow.print();
          printWindow.close();
        }, 500);
        break;

      case "share":
        // Convert data URL to blob for sharing
        fetch(exportDataURL)
          .then((res) => res.blob())
          .then(async (blob) => {
            if (!blob) return;
            try {
              const file = new File([blob], "colored-image.png", {
                type: "image/png",
              });

              if (
                navigator.share &&
                navigator.canShare &&
                navigator.canShare({ files: [file] })
              ) {
                await navigator.share({
                  files: [file],
                  title: "My Colored Image",
                });
              } else {
                // Fallback to download if sharing is not supported
                const link = document.createElement("a");
                link.download = "colored-image.png";
                link.href = URL.createObjectURL(blob);
                link.click();
                URL.revokeObjectURL(link.href); // Clean up object URL
              }
            } catch (err) {
              console.error("Error sharing:", err);
              // Fallback download on share error
              const link = document.createElement("a");
              link.download = "colored-image.png";
              link.href = URL.createObjectURL(blob);
              link.click();
              URL.revokeObjectURL(link.href);
            }
          })
          .catch((err) => {
            console.error("Error fetching blob for sharing:", err);
          });
        break;
    }
  }

  // Initialize event listeners
  function initEventListeners() {
    // Canvas drawing events
    if (canvas) {
      canvas.addEventListener("mousedown", startDrawing);
      canvas.addEventListener("mousemove", draw);
      canvas.addEventListener("mouseup", stopDrawing);
      canvas.addEventListener("mouseout", stopDrawing);

      // Touch events for mobile
      canvas.addEventListener("touchstart", (e) => {
        e.preventDefault();
        startDrawing(e);
      });
      canvas.addEventListener("touchmove", (e) => {
        e.preventDefault();
        draw(e);
      });
      canvas.addEventListener("touchend", stopDrawing);
    }

    // Color palette events
    colorOptions.forEach((option) => {
      option.addEventListener("click", () => {
        const color = option.getAttribute("data-color");
        if (color) {
          currentColor = color;
          lastSelectedColor = color; // Remember this color
          lastActiveColorOption = option; // Remember this color option
          lastActiveColorPickerIndex = null; // Reset color picker memory

          // Deactivate eraser
          isErasing = false;
          eraserBtn?.classList.remove("active");

          // Update active state for colors
          colorOptions.forEach((opt) => opt.classList.remove("active"));
          option.classList.add("active");

          // Remove active state from color pickers
          updateCustomColorPickerVisuals();
        }
      });
    });

    // Color picker events (input change)
    colorPicker1?.addEventListener("change", (e) => {
      customColor1 = e.target.value;
      currentColor = customColor1;
      lastSelectedColor = customColor1; // Remember this color
      lastActiveColorOption = null; // Reset color option memory
      lastActiveColorPickerIndex = 1; // Remember this color picker

      // Deactivate eraser
      isErasing = false;
      eraserBtn?.classList.remove("active");

      // Update color palette active state
      colorOptions.forEach((opt) => opt.classList.remove("active"));

      // Update custom color picker visuals (active state and background)
      updateCustomColorPickerVisuals(1);
    });

    colorPicker2?.addEventListener("change", (e) => {
      customColor2 = e.target.value;
      currentColor = customColor2;
      lastSelectedColor = customColor2; // Remember this color
      lastActiveColorOption = null; // Reset color option memory
      lastActiveColorPickerIndex = 2; // Remember this color picker

      // Deactivate eraser
      isErasing = false;
      eraserBtn?.classList.remove("active");

      // Update color palette active state
      colorOptions.forEach((opt) => opt.classList.remove("active"));

      // Update custom color picker visuals (active state and background)
      updateCustomColorPickerVisuals(2);
    });

    // New interaction logic for Custom Color Picker 1
    customColorPicker1Container?.addEventListener("click", (e) => {
      // Normal click (left click) - select the color
      if (!longPressTimer) {
        // Ensure it wasn't triggered by long press logic
        currentColor = customColor1;
        lastSelectedColor = customColor1;
        lastActiveColorOption = null;
        lastActiveColorPickerIndex = 1;
        isErasing = false;
        eraserBtn?.classList.remove("active");
        colorOptions.forEach((opt) => opt.classList.remove("active"));
        updateCustomColorPickerVisuals(1);
      }
    });

    customColorPicker1Container?.addEventListener("contextmenu", (e) => {
      // Right click - open the color picker
      e.preventDefault(); // Prevent context menu
      triggerColorInputClick(colorPicker1);
    });

    customColorPicker1Container?.addEventListener("touchstart", (e) => {
      e.preventDefault(); // Prevent scrolling/other default actions
      longPressTimer = setTimeout(() => {
        triggerColorInputClick(colorPicker1);
        longPressTimer = null; // Reset timer after firing
      }, longPressDuration);
    });

    customColorPicker1Container?.addEventListener("touchend", (e) => {
      if (longPressTimer) {
        clearTimeout(longPressTimer);
        longPressTimer = null;
        // If timer still existed, it was a short tap
        currentColor = customColor1;
        lastSelectedColor = customColor1;
        lastActiveColorOption = null;
        lastActiveColorPickerIndex = 1;
        isErasing = false;
        eraserBtn?.classList.remove("active");
        colorOptions.forEach((opt) => opt.classList.remove("active"));
        updateCustomColorPickerVisuals(1);
      }
      // If longPressTimer is null here, it means the long press already fired
    });
    customColorPicker1Container?.addEventListener("touchcancel", (e) => {
      clearTimeout(longPressTimer);
      longPressTimer = null;
    });

    // New interaction logic for Custom Color Picker 2
    customColorPicker2Container?.addEventListener("click", (e) => {
      if (!longPressTimer) {
        // Ensure it wasn't triggered by long press logic
        currentColor = customColor2;
        lastSelectedColor = customColor2;
        lastActiveColorOption = null;
        lastActiveColorPickerIndex = 2;
        isErasing = false;
        eraserBtn?.classList.remove("active");
        colorOptions.forEach((opt) => opt.classList.remove("active"));
        updateCustomColorPickerVisuals(2);
      }
    });

    customColorPicker2Container?.addEventListener("contextmenu", (e) => {
      e.preventDefault();
      triggerColorInputClick(colorPicker2);
    });

    customColorPicker2Container?.addEventListener("touchstart", (e) => {
      e.preventDefault();
      longPressTimer = setTimeout(() => {
        triggerColorInputClick(colorPicker2);
        longPressTimer = null;
      }, longPressDuration);
    });

    customColorPicker2Container?.addEventListener("touchend", (e) => {
      if (longPressTimer) {
        clearTimeout(longPressTimer);
        longPressTimer = null;
        currentColor = customColor2;
        lastSelectedColor = customColor2;
        lastActiveColorOption = null;
        lastActiveColorPickerIndex = 2;
        isErasing = false;
        eraserBtn?.classList.remove("active");
        colorOptions.forEach((opt) => opt.classList.remove("active"));
        updateCustomColorPickerVisuals(2);
      }
    });
    customColorPicker2Container?.addEventListener("touchcancel", (e) => {
      clearTimeout(longPressTimer);
      longPressTimer = null;
    });

    // Renamed and updated function to handle custom picker visuals
    function updateCustomColorPickerVisuals(activeIndex = null) {
      if (customColorPicker1Container && customColorDisplay1) {
        customColorDisplay1.style.backgroundColor = customColor1;
        if (activeIndex === 1) {
          customColorPicker1Container.classList.add("active");
        } else {
          customColorPicker1Container.classList.remove("active");
        }
      }

      if (customColorPicker2Container && customColorDisplay2) {
        customColorDisplay2.style.backgroundColor = customColor2;
        if (activeIndex === 2) {
          customColorPicker2Container.classList.add("active");
        } else {
          customColorPicker2Container.classList.remove("active");
        }
      }
    }

    // Fill mode button - works with current brush or eraser
    fillBtn?.addEventListener("click", () => {
      // Toggle fill mode
      isFillMode = !isFillMode;

      // Update brush size active states
      brushSizes.forEach((b) => b.classList.remove("active"));

      if (isFillMode) {
        // Activate fill mode
        fillBtn.classList.add("active");
      } else {
        // Deactivate fill mode
        fillBtn.classList.remove("active");

        // Restore the last selected brush size
        if (lastActiveBrushButton) {
          // Restore last selected brush size
          brushSize = lastSelectedBrushSize;
          brushSizes.forEach((b) => b.classList.remove("active"));
          lastActiveBrushButton.classList.add("active");
        } else {
          // Fallback to small brush if no last brush size is remembered
          const smallBrush = document.getElementById("smallBrush");
          if (smallBrush) {
            brushSize = 6; // Small brush size
            brushSizes.forEach((b) => b.classList.remove("active"));
            smallBrush.classList.add("active");
          }
        }
      }
    });

    // Eraser button - works in both brush and fill modes
    eraserBtn?.addEventListener("click", () => {
      isErasing = !isErasing;

      if (isErasing) {
        // Activate eraser
        eraserBtn.classList.add("active");

        // Deactivate color selection but remember current state
        colorOptions.forEach((opt) => opt.classList.remove("active"));
        updateCustomColorPickerVisuals();
      } else {
        // Deactivate eraser
        eraserBtn.classList.remove("active");

        // Restore the last selected color
        if (lastActiveColorOption) {
          // Restore last selected color from palette
          currentColor = lastSelectedColor;
          colorOptions.forEach((opt) => opt.classList.remove("active"));
          lastActiveColorOption.classList.add("active");
          updateCustomColorPickerVisuals();
        } else if (lastActiveColorPickerIndex) {
          // Restore last selected custom color
          currentColor = lastSelectedColor;
          colorOptions.forEach((opt) => opt.classList.remove("active"));
          updateCustomColorPickerVisuals(lastActiveColorPickerIndex);
        } else {
          // Fallback to first color if no last color is remembered
          const firstColor = document.querySelector(".color-option");
          if (firstColor) {
            currentColor = firstColor.getAttribute("data-color") || "#FF0000";
            colorOptions.forEach((opt) => opt.classList.remove("active"));
            firstColor.classList.add("active");
          }
        }
      }
    });

    // Undo button
    undoBtn?.addEventListener("click", undo);

    // Redo button
    redoBtn?.addEventListener("click", redo);

    // Brush size buttons
    brushSizes.forEach((btn) => {
      btn.addEventListener("click", () => {
        const size = btn.getAttribute("data-size");

        // Set brush size based on the selected button
        switch (size) {
          case "extraSmall":
            brushSize = 3;
            break;
          case "small":
            brushSize = 6;
            break;
          case "medium":
            brushSize = 10;
            break;
          case "large":
            brushSize = 15;
            break;
          default:
            brushSize = 6;
        }

        // Remember this brush size and button
        lastSelectedBrushSize = brushSize;
        lastActiveBrushButton = btn;

        // Deactivate fill mode
        isFillMode = false;
        fillBtn?.classList.remove("active");

        // Update active state for brush sizes
        brushSizes.forEach((b) => b.classList.remove("active"));
        btn.classList.add("active");
      });
    });

    // Export buttons
    downloadBtn?.addEventListener("click", () => exportCanvas("download"));
    printBtn?.addEventListener("click", () => exportCanvas("print"));
    shareBtn?.addEventListener("click", () => exportCanvas("share"));

    // Debounce function for resize handler
    const debounce = (func, delay) => {
      let debounceTimer;
      return function () {
        const context = this;
        const args = arguments;
        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(() => func.apply(context, args), delay);
      };
    };

    // Window resize event with debouncing
    const debouncedResize = debounce(handleResize, 250);
    window.addEventListener("resize", debouncedResize);

    // Orientation change event for mobile devices
    if (window.matchMedia) {
      // Listen for orientation changes
      const mediaQuery = window.matchMedia("(orientation: portrait)");

      // Modern approach using addEventListener
      try {
        // Chrome, Firefox
        mediaQuery.addEventListener("change", () => {
          // Wait a moment for the browser to adjust after rotation
          setTimeout(handleResize, 300);
        });
      } catch (e1) {
        try {
          // Safari, older browsers
          mediaQuery.addListener(() => {
            setTimeout(handleResize, 300);
          });
        } catch (e2) {
          console.error("Browser doesn't support orientation change events");
        }
      }
    }
  }

  // Initialize
  function init() {
    // Wait for image to load before setting up canvas
    if (sourceImage.complete) {
      setupCanvas();
      initEventListeners();
      setDefaultState();
      setTooltips();
    } else {
      sourceImage.onload = () => {
        setupCanvas();
        initEventListeners();
        setDefaultState();
        setTooltips();
      };
    }

    // Initialize undo/redo buttons state
    updateUndoRedoButtons();
  }

  // Set default state: first color selected, second brush (small) selected
  function setDefaultState() {
    // Set first color as active
    const firstColor = document.querySelector(".color-option");
    if (firstColor) {
      firstColor.classList.add("active");
      currentColor = firstColor.getAttribute("data-color") || "#FF0000";
      lastSelectedColor = currentColor;
      lastActiveColorOption = firstColor;
    }

    // Set small brush as active
    const smallBrush = document.getElementById("smallBrush");
    if (smallBrush) {
      brushSizes.forEach((b) => b.classList.remove("active"));
      smallBrush.classList.add("active");
      brushSize = 6; // Small brush size
      lastSelectedBrushSize = brushSize;
      lastActiveBrushButton = smallBrush;
    }

    // Initialize custom color picker visuals on load
    const updateCustomColorPickerVisuals = (activeIndex = null) => {
      if (customColorPicker1Container && customColorDisplay1) {
        customColorDisplay1.style.backgroundColor = customColor1;
        if (activeIndex === 1) {
          customColorPicker1Container.classList.add("active");
        } else {
          customColorPicker1Container.classList.remove("active");
        }
      }

      if (customColorPicker2Container && customColorDisplay2) {
        customColorDisplay2.style.backgroundColor = customColor2;
        if (activeIndex === 2) {
          customColorPicker2Container.classList.add("active");
        } else {
          customColorPicker2Container.classList.remove("active");
        }
      }
    };
    // Apply initial background colors
    updateCustomColorPickerVisuals();

    // Note: The active state for custom pickers is handled by user interaction,
    // so we don't set one as active initially unless required.
  }

  // Helper function to trigger the color input click
  function triggerColorInputClick(inputElement) {
    inputElement?.click();
  }

  // Set platform-specific tooltips
  function setTooltips() {
    const isTouchDevice =
      "ontouchstart" in window || navigator.maxTouchPoints > 0;

    const desktopTooltip = "Left click to select, right click to change color";
    const mobileTooltip = "Tap to select, long tap to change color";
    const tooltipText = isTouchDevice ? mobileTooltip : desktopTooltip;

    if (customColorPicker1Container) {
      customColorPicker1Container.title = tooltipText;
    }
    if (customColorPicker2Container) {
      customColorPicker2Container.title = tooltipText;
    }
  }

  // Run initialization
  init();
}
