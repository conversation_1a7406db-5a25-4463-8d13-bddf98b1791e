---
import {
  getMainCategories,
  getDisplayName,
  getCategoryUri,
} from "../../utils/collections";

const categories = await getMainCategories();
---

<header class="sticky top-0 z-50 bg-white/95 backdrop-blur-sm shadow-sm">
  <div class="container mx-auto px-4">
    <div class="navbar min-h-[70px]">
      <div class="navbar-start">
        <div class="dropdown md:hidden">
          <div tabindex="0" role="button" class="btn btn-ghost">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 6h16M4 12h8m-8 6h16"></path>
            </svg>
          </div>
          <ul
            tabindex="0"
            class="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52">
            <li><a href="/" class="font-semibold">Home</a></li>
            <li>
              <a class="font-semibold">Categories</a>
              <ul class="p-2">
                {
                  categories.map((category) => (
                    <li>
                      <a href={getCategoryUri(category)}>
                        {getDisplayName(category)}
                      </a>
                    </li>
                  ))
                }
              </ul>
            </li>
            <li><a href="/about/" class="font-semibold">About</a></li>
            <li><a href="/contact/" class="font-semibold">Contact</a></li>
          </ul>
        </div>
        <a href="/" class="btn btn-ghost text-xl md:text-2xl">
          <span
            class="inline-block bg-gradient-to-r from-[#1E90FF] to-[#8A2BE2] text-transparent bg-clip-text font-prompt">
            <span class="font-bold"
              ><span class="text-[1.15em]">C</span>oloring</span
            ><span class="font-bold"
              ><span class="text-[1.15em]">M</span>ix</span
            >
          </span>
        </a>
      </div>
      <div class="navbar-center hidden md:flex">
        <ul class="menu menu-horizontal px-1">
          <li><a href="/" class="font-semibold">Home</a></li>
          <li>
            <details>
              <summary class="font-semibold">Categories</summary>
              <ul class="p-2 bg-base-100 rounded-box w-48 shadow-md z-10">
                {
                  categories.map((category) => (
                    <li>
                      <a href={getCategoryUri(category)}>
                        {getDisplayName(category)}
                      </a>
                    </li>
                  ))
                }
              </ul>
            </details>
          </li>
          <li><a href="/about/" class="font-semibold">About</a></li>
          <li><a href="/contact/" class="font-semibold">Contact</a></li>
        </ul>
      </div>
      <div class="navbar-end">
        &nbsp;
        <!-- 
        <div class="form-control mr-2">
          <input
            type="text"
            placeholder="Search coloring pages"
            class="input input-bordered w-24 md:w-auto focus:border-primary-500"
          />
        </div>
        <a href="/favorites/" class="btn btn-ghost btn-circle">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
            ></path>
          </svg>
        </a>
         -->
      </div>
    </div>
  </div>
</header>

<script>
  document.addEventListener("click", (event) => {
    const detailsEl = document.querySelector("header .navbar-center details");
    if (!(detailsEl instanceof HTMLDetailsElement)) {
      return;
    }
    if (!detailsEl.open) {
      return;
    }
    const target = event.target;
    if (target instanceof Node && !detailsEl.contains(target)) {
      detailsEl.open = false;
    }
  });
</script>
