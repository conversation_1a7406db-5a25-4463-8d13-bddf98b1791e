/**
 * Utility functions for working with Astro content collections.
 *
 * IMPORTANT DESIGN PHILOSOPHY:
 * All category and coloring page entities use their file/folder names as their primary
 * identifier rather than having an explicit "name" field in the schema.
 * These names/slugs from the filesystem are used for:
 * 1. Generating readable display names via formatting (e.g., "wild-animals" → "Wild Animals")
 * 2. Constructing URLs and file paths
 * 3. Referencing related entities
 *
 * This approach has several benefits:
 * - Removes duplication between filesystem organization and content
 * - Ensures consistency across the application
 * - Simplifies content management (the name of an entity is its file name)
 * - Makes URL structure predictable and SEO-friendly
 *
 * The utility functions in this file handle the mapping between these filesystem-based
 * identifiers and their various representations throughout the application.
 */
import { getCollection } from "astro:content";
import type { CollectionEntry } from "astro:content";

// Types for better code organization
export type CategoryEntry = CollectionEntry<"categories">;
export type ColoringPageEntry = CollectionEntry<"coloring-pages">;

/**
 * Cache for collections to avoid multiple fetches during a single page load
 * This improves performance, especially when multiple components need the same data
 */
let categoriesCache: CategoryEntry[] | null = null;
let coloringPagesCache: ColoringPageEntry[] | null = null;

/**
 * Get all categories
 *
 * The returned entries contain both the data defined in the frontmatter and
 * metadata like 'slug' which comes from the file name/location
 */
export async function getAllCategories(): Promise<CategoryEntry[]> {
  if (!categoriesCache) {
    categoriesCache = await getCollection("categories");
  }
  return categoriesCache;
}

/**
 * Get all coloring pages
 *
 * The returned entries contain both the data defined in the frontmatter and
 * metadata like 'slug' which comes from the file name/location
 */
export async function getAllColoringPages(): Promise<ColoringPageEntry[]> {
  if (!coloringPagesCache) {
    coloringPagesCache = await getCollection("coloring-pages");
  }
  return coloringPagesCache;
}

/**
 * Get main categories (those directly in the categories folder)
 *
 * Main categories are those that don't have a slash in their slug,
 * meaning they are top-level categories in the hierarchy
 */
export async function getMainCategories(): Promise<CategoryEntry[]> {
  const categories = await getAllCategories();
  return categories.filter((category) => !category.slug.includes("/"));
}

/**
 * Get featured categories
 *
 * Featured categories are highlighted on the homepage or in special sections
 */
export async function getFeaturedCategories(): Promise<CategoryEntry[]> {
  const categories = await getAllCategories();
  return categories.filter((category) => category.data.featured);
}

/**
 * Get subcategories for a given parent category
 *
 * Finds all categories that have their slug starting with the parent slug followed by a slash.
 * The parentSlug parameter should match a category's filename/slug (not a path).
 */
export async function getSubcategories(
  parentSlug: string
): Promise<CategoryEntry[]> {
  const categories = await getAllCategories();
  return categories.filter((category) =>
    category.slug.startsWith(`${parentSlug}/`)
  );
}

/**
 * Get a category by its slug
 *
 * The slug parameter should match the category's filename.
 * For example, for a file at "categories/animals.md", the slug would be "animals".
 */
export async function getCategoryBySlug(
  slug: string
): Promise<CategoryEntry | undefined> {
  const categories = await getAllCategories();
  return categories.find((category) => category.slug === slug);
}

/**
 * Get coloring pages by category and subcategory
 *
 * Filters pages based on their file path structure.
 * For example, a file at "coloring-pages/animals/wild/lion.md" would be in
 * category "animals" and subcategory "wild".
 */
export async function getColoringPagesByCategory(
  category: string,
  subcategory?: string
): Promise<ColoringPageEntry[]> {
  const pages = await getAllColoringPages();
  return pages.filter((page) => {
    // Split the slug by slashes to get the path segments
    const pathSegments = page.slug.split("/");

    // A valid coloring page should have at least 2 segments (category/subcategory/name)
    if (pathSegments.length < 2) return false;

    // The first segment is the category
    const pageCategory = pathSegments[0];

    if (subcategory) {
      // The second segment is the subcategory
      const pageSubcategory = pathSegments[1];
      return pageCategory === category && pageSubcategory === subcategory;
    }

    return pageCategory === category;
  });
}

/**
 * Get featured coloring pages
 *
 * Featured pages are highlighted on the homepage or in special sections
 */
export async function getFeaturedColoringPages(): Promise<ColoringPageEntry[]> {
  const pages = await getAllColoringPages();
  return pages.filter((page) => page.data.featured);
}

/**
 * Get a coloring page by its slug
 *
 * The slug parameter should match the coloring page's filename.
 * For example, for a file at "coloring-pages/animals/wild/lion.md", the slug would be "lion".
 */
export async function getColoringPageBySlug(
  slug: string
): Promise<ColoringPageEntry | undefined> {
  const pages = await getAllColoringPages();
  return pages.find((page) => page.slug === slug);
}

/**
 * Get the URI for a category
 *
 * Converts a category slug (from the filename) into a URL path.
 * This maintains the connection between filesystem naming and URL structure.
 */
export function getCategoryUri(category: CategoryEntry | string): string {
  const slug = typeof category === "string" ? category : category.slug;
  return `/${slug}/`;
}

/**
 * Get the URI for a coloring page
 *
 * Creates a URL path based on the category, subcategory, and the page's slug (filename).
 * The consistent pattern makes URLs predictable and SEO-friendly.
 */
export function getColoringPageUri(page: ColoringPageEntry): string {
  // Split the slug by slashes to get the path segments
  const pathSegments = page.slug.split("/");

  // A valid coloring page should have at least 2 segments (category/subcategory/name)
  if (pathSegments.length < 2) {
    console.error(`Invalid coloring page slug: ${page.slug}`);
    return "/";
  }

  // The first segment is the category
  const category = pathSegments[0];

  // The second segment is the subcategory
  const subcategory = pathSegments[1];

  // The last segment is the page name
  const pageName = pathSegments[pathSegments.length - 1];

  return `/${category}/${subcategory}/${pageName}-coloring-page/`;
}

/**
 * Get the thumbnail path for a category
 *
 * Maps category slugs (from filenames) to their corresponding thumbnail image paths.
 * This convention ensures consistent asset organization based on entity names.
 * Note: Currently returns the same path as the main image since thumbnails use the same file.
 */
export function getCategoryThumbnailPath(
  category: CategoryEntry | string
): string {
  let slug = typeof category === "string" ? category : category.slug;

  // For subcategories (slug contains a slash), extract the parent and child parts
  if (slug.includes("/")) {
    const parts = slug.split("/");
    const parentSlug = parts[0];
    const childSlug = parts[parts.length - 1];
    return `/src/assets/images/${parentSlug}/${childSlug}.png`;
  }

  // For main categories (no slash in slug)
  return `/src/assets/images/${slug}.png`;
}

/**
 * Get the image path for a category
 *
 * Maps category slugs (from filenames) to their corresponding image paths.
 * This convention ensures consistent asset organization based on entity names.
 */
export function getCategoryImagePath(category: CategoryEntry | string): string {
  let slug = typeof category === "string" ? category : category.slug;

  // For subcategories (slug contains a slash), extract the parent and child parts
  if (slug.includes("/")) {
    const parts = slug.split("/");
    const parentSlug = parts[0];
    const childSlug = parts[parts.length - 1];
    return `/src/assets/images/${parentSlug}/${childSlug}.png`;
  }

  // For main categories (no slash in slug)
  return `/src/assets/images/${slug}.png`;
}

/**
 * Get the thumbnail path for a coloring page
 *
 * Maps coloring page metadata to its thumbnail image path.
 * Uses the category, subcategory, and slug (filename) to create a predictable path.
 * Note: Currently returns the same path as the main image since thumbnails use the same file.
 */
export function getColoringPageThumbnailPath(page: ColoringPageEntry): string {
  // Split the slug by slashes to get the path segments
  const pathSegments = page.slug.split("/");

  // A valid coloring page should have at least 2 segments (category/subcategory/name)
  if (pathSegments.length < 2) {
    console.error(`Invalid coloring page slug: ${page.slug}`);
    return "/src/assets/images/placeholder.png";
  }

  // The first segment is the category
  const category = pathSegments[0];

  // The second segment is the subcategory
  const subcategory = pathSegments[1];

  // The last segment is the page name
  const pageName = pathSegments[pathSegments.length - 1];

  return `/src/assets/images/${category}/${subcategory}/${pageName}.png`;
}

/**
 * Get the image path for a coloring page
 *
 * Maps coloring page metadata to its full-size image path.
 * Uses the category, subcategory, and slug (filename) to create a predictable path.
 */
export function getColoringPageImagePath(page: ColoringPageEntry): string {
  // Split the slug by slashes to get the path segments
  const pathSegments = page.slug.split("/");

  // A valid coloring page should have at least 2 segments (category/subcategory/name)
  if (pathSegments.length < 2) {
    console.error(`Invalid coloring page slug: ${page.slug}`);
    return "/src/assets/images/placeholder.png";
  }

  // The first segment is the category
  const category = pathSegments[0];

  // The second segment is the subcategory
  const subcategory = pathSegments[1];

  // The last segment is the page name
  const pageName = pathSegments[pathSegments.length - 1];

  return `/src/assets/images/${category}/${subcategory}/${pageName}.png`;
}

/**
 * Get the display name (title) for a category or coloring page
 *
 * Converts a slug (derived from filename) into a user-friendly display name.
 * This is a key function that transforms the filesystem-based identifier into
 * something presentable to users, demonstrating how a single source of truth
 * (the filename) can serve multiple purposes.
 *
 * Example: "wild-animals" → "Wild Animals"
 */
export function getDisplayName(
  slugOrEntry: string | CategoryEntry | ColoringPageEntry
): string {
  let slug: string;
  let name: string | undefined;

  if (typeof slugOrEntry === "string") {
    slug = slugOrEntry;
    name = undefined; // No name available if only slug is provided
  } else {
    // Assume it's a collection entry object
    slug = slugOrEntry.slug;
    name = slugOrEntry.data.name;
  }

  // If a specific name is provided in the frontmatter, use it
  if (name) {
    return name;
  }

  // Otherwise, format the slug
  // Handle nested slugs like 'animals/wild' by taking the last part
  const parts = slug.split("/");
  const displaySlug = parts[parts.length - 1];

  return displaySlug
    .split("-")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}

/**
 * Clear the caches (useful for development or when content changes)
 *
 * Resets the internal caches to ensure fresh data is fetched on the next request.
 */
export function clearCaches(): void {
  categoriesCache = null;
  coloringPagesCache = null;
}
