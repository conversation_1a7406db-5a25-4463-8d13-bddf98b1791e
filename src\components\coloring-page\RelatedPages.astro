---
import ColoringPageCard from "../common/ColoringPageCard.astro";
import {
  getColoringPagesByCategory,
  getDisplayName,
  getColoringPageThumbnailPath,
  getColoringPageUri,
} from "../../utils/collections";

interface Props {
  categorySlug: string;
  subcategorySlug?: string;
  currentSlug: string;
}

const { categorySlug, subcategorySlug, currentSlug } = Astro.props;
let relatedPages = [];

if (subcategorySlug) {
  // If subcategory is provided, get pages from that specific subcategory
  relatedPages = await getColoringPagesByCategory(
    categorySlug,
    subcategorySlug
  );
} else {
  // Otherwise get all pages from the category
  relatedPages = await getColoringPagesByCategory(categorySlug);
}

// Filter out the current page and limit to 4 pages
relatedPages = relatedPages
  .filter((page) => page.slug !== currentSlug)
  .slice(0, 4);

function toTitleCase(str: string): string {
  return str
    .replace(/-/g, " ")
    .replace(/\b\w/g, (c: string) => c.toUpperCase());
}
---

{
  relatedPages.length > 0 && (
    <section class="mt-12">
      <h2 class="text-2xl font-bold mb-6">More Coloring Pages You'll Love</h2>
      <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {relatedPages.map((page) => (
          <ColoringPageCard
            id={page.slug}
            title={getDisplayName(page)}
            description={page.data.description}
            thumbnail={getColoringPageThumbnailPath(page)}
            categoryId={page.slug.split("/")[0]}
            subcategoryId={page.slug.split("/")[1]}
            difficulty={page.data.difficulty}
            slug={page.slug.split("/").pop() || ""}
            url={getColoringPageUri(page)}
          />
        ))}
      </div>
    </section>
  )
}
