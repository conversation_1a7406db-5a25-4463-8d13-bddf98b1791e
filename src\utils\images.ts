/**
 * Centralized image management for Astro Assets
 * 
 * This module provides utilities for working with dynamically imported images
 * using Astro's asset system. It uses import.meta.glob() to import all images
 * and provides helper functions to access them by path.
 */

import type { ImageMetadata } from 'astro';

/**
 * Import all images from the assets directory using glob pattern
 * This creates a record of all available images that can be accessed dynamically
 */
const images = import.meta.glob<{ default: ImageMetadata }>('/src/assets/images/**/*.{png,jpg,jpeg,gif,webp}');

/**
 * Get an image object by its path
 * 
 * @param imagePath - The path to the image relative to src/assets/images
 * @returns Promise that resolves to the ImageMetadata object
 * @throws Error if the image doesn't exist
 */
export async function getImage(imagePath: string): Promise<ImageMetadata> {
  // Ensure the path starts with /src/assets/images
  const fullPath = imagePath.startsWith('/src/assets/images') 
    ? imagePath 
    : `/src/assets/images/${imagePath}`;

  const imageImporter = images[fullPath];
  
  if (!imageImporter) {
    console.error(`Image not found: ${fullPath}`);
    console.error('Available images:', Object.keys(images));
    throw new Error(`Image not found: ${fullPath}. Make sure the image exists in the assets directory.`);
  }

  const imageModule = await imageImporter();
  return imageModule.default;
}

/**
 * Get an image object by its path (synchronous version)
 * 
 * @param imagePath - The path to the image relative to src/assets/images
 * @returns Function that when called returns a Promise resolving to ImageMetadata
 * @throws Error if the image doesn't exist
 */
export function getImageImporter(imagePath: string): () => Promise<{ default: ImageMetadata }> {
  // Ensure the path starts with /src/assets/images
  const fullPath = imagePath.startsWith('/src/assets/images') 
    ? imagePath 
    : `/src/assets/images/${imagePath}`;

  const imageImporter = images[fullPath];
  
  if (!imageImporter) {
    console.error(`Image not found: ${fullPath}`);
    console.error('Available images:', Object.keys(images));
    throw new Error(`Image not found: ${fullPath}. Make sure the image exists in the assets directory.`);
  }

  return imageImporter;
}

/**
 * Check if an image exists at the given path
 * 
 * @param imagePath - The path to check
 * @returns boolean indicating if the image exists
 */
export function imageExists(imagePath: string): boolean {
  const fullPath = imagePath.startsWith('/src/assets/images') 
    ? imagePath 
    : `/src/assets/images/${imagePath}`;
  
  return fullPath in images;
}

/**
 * Get all available image paths
 * 
 * @returns Array of all available image paths
 */
export function getAllImagePaths(): string[] {
  return Object.keys(images);
}

/**
 * Get images that match a pattern
 * 
 * @param pattern - RegExp pattern to match against image paths
 * @returns Array of matching image paths
 */
export function getImagesByPattern(pattern: RegExp): string[] {
  return Object.keys(images).filter(path => pattern.test(path));
}
