---
import Layout from "../../../layouts/Layout.astro";
import { Image } from "astro:assets";
import RelatedCategories from "../../../components/coloring-page/RelatedCategories.astro";
import RelatedPages from "../../../components/coloring-page/RelatedPages.astro";
import {
  getAllColoringPages,
  getCategoryBySlug,
  getDisplayName,
  getColoringPageImagePath,
} from "../../../utils/collections";

export async function getStaticPaths() {
  const pages = await getAllColoringPages();
  return pages
    .map((page) => {
      // Split the slug to get category, subcategory, and page name
      const pathSegments = page.slug.split("/");

      // A valid coloring page should have at least 2 segments (category/subcategory/name)
      if (pathSegments.length < 2) {
        console.error(`Invalid coloring page slug: ${page.slug}`);
        return null;
      }

      // The first segment is the category
      const category = pathSegments[0];

      // The second segment is the subcategory
      const subcategory = pathSegments[1];

      // The last segment is the page name
      const pageName = pathSegments[pathSegments.length - 1];
      const urlSlug = `${pageName}-coloring-page`;

      return {
        params: {
          category: category,
          subcategory: subcategory,
          slug: urlSlug,
        },
        props: { page },
      };
    })
    .filter(Boolean); // Filter out any null entries
}

const { page } = Astro.props;
const { category, subcategory } = Astro.params;

const urlSlug = Astro.params.slug;
const originalSlug = urlSlug?.endsWith("-coloring-page")
  ? urlSlug.slice(0, -14)
  : urlSlug;

const slug = page.slug.includes("/") ? page.slug.split("/").pop() : page.slug;

const categoryEntry = await getCategoryBySlug(category || "");
const subcategoryEntry = await getCategoryBySlug(subcategory || "");

// Get the image path using the utility function
const imagePath = getColoringPageImagePath(page);

// Use the display name utility for the title
const title = getDisplayName(page);
const description = page.data.description;
---

<Layout title={title} description={description}>
  <div class="container mx-auto px-4 py-8">
    <div class="text-sm breadcrumbs mb-6">
      <ul>
        <li><a href="/">Home</a></li>
        {
          categoryEntry && (
            <li>
              <a href={`/${categoryEntry.slug}/`}>
                {getDisplayName(categoryEntry)}
              </a>
            </li>
          )
        }
        {
          subcategoryEntry && (
            <li>
              <a
                href={`/${categoryEntry?.slug}/${subcategoryEntry.slug.split("/").pop()}/`}>
                {getDisplayName(subcategoryEntry)}
              </a>
            </li>
          )
        }
        <li>{title}</li>
      </ul>
    </div>

    <header class="mb-8 text-center md:text-left">
      <h1 class="text-3xl md:text-4xl font-bold mb-4">{title}</h1>
      <p class="text-lg text-neutral-600 max-w-3xl">{description}</p>

      <div class="mt-4 flex flex-wrap items-center gap-4">
        <span class="inline-flex items-center">
          <span class="font-semibold mr-2">Difficulty:</span>
          <span class="badge badge-primary">{page.data.difficulty}</span>
        </span>

        {
          page.data.featured && (
            <span class="inline-block bg-yellow-200 text-yellow-800 px-3 py-1 rounded text-sm font-bold">
              Featured
            </span>
          )
        }
      </div>
    </header>

    <!-- Action Buttons -->
    <div class="action-buttons-container mb-4 flex justify-center gap-2">
      <button class="btn btn-primary" id="downloadBtn" title="Download">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
          ></path>
        </svg>
        Download
      </button>
      <button class="btn btn-primary" id="printBtn" title="Print">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"
          ></path>
        </svg>
        Print
      </button>
      <button class="btn btn-primary" id="shareBtn" title="Share">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"
          ></path>
        </svg>
        Share
      </button>
    </div>

    <!-- Coloring Area -->
    <section
      class="mb-12 relative coloring-area-container flex justify-center items-center">
      <!-- Static View (Initially Visible) -->
      <div
        id="static-coloring-view"
        class="relative w-full max-w-[800px] mx-auto">
        <!-- Static Image -->
        <img
          id="static-coloring-image"
          src={imagePath}
          alt={title}
          class="block w-full h-auto border border-gray-300 rounded shadow-md"
          width="800"
          height="600"
        />

        <!-- Top Button Container -->
        <div class="absolute top-4 left-0 right-0 flex justify-center">
          <button
            id="start-coloring-btn-top"
            class="start-coloring-btn btn btn-warning shadow-lg">
            🎨 Color Now!
          </button>
        </div>

        <!-- Bottom Button Container -->
        <div class="absolute bottom-4 left-0 right-0 flex justify-center">
          <button
            id="start-coloring-btn-bottom"
            class="start-coloring-btn btn btn-warning shadow-lg">
            🎨 Color Now!
          </button>
        </div>

        <!-- Loading Spinner (Initially Hidden) -->
        <div
          id="loading-spinner"
          class="absolute inset-0 bg-white bg-opacity-80 justify-center items-center hidden z-50">
          <span class="loading loading-spinner loading-lg text-primary"></span>
        </div>
      </div>

      <!-- Interactive View (Initially Hidden) -->
      <div id="interactive-coloring-view" class="hidden w-full">
        {/* Placeholder for interactive coloring view */}
      </div>
    </section>

    {
      page.body && (
        <section class="mb-12">
          <div class="prose max-w-3xl mx-auto">{page.body}</div>
        </section>
      )
    }

    <RelatedPages
      categorySlug={category || ""}
      subcategorySlug={subcategory || ""}
      currentSlug={originalSlug || ""}
    />
    <RelatedCategories categorySlug={category || ""} />
  </div>

  <script define:vars={{ staticImageUrl: imagePath, pageTitle: title }}>
    // Wrap all logic in a function to be called on page load/navigation
    function initializeColoringPage() {
      console.log("Initializing/Re-initializing coloring page script...");

      // --- Force reset to static view state ---
      const staticView = document.getElementById("static-coloring-view");
      const interactiveView = document.getElementById(
        "interactive-coloring-view"
      );
      const loadingSpinner = document.getElementById("loading-spinner");
      const startButtonTop = document.getElementById("start-coloring-btn-top");
      const startButtonBottom = document.getElementById(
        "start-coloring-btn-bottom"
      );

      // Ensure static view is shown, interactive is hidden
      staticView?.classList.remove("hidden");
      interactiveView?.classList.add("hidden");
      // Clear potential leftover content in interactive view
      if (interactiveView) interactiveView.innerHTML = "";
      // Hide spinner
      loadingSpinner?.classList.add("hidden");
      loadingSpinner?.classList.remove("flex");
      // Re-enable start buttons if they exist
      if (startButtonTop) startButtonTop.disabled = false;
      if (startButtonBottom) startButtonBottom.disabled = false;

      // --- Get original buttons ---
      const originalStartButtonTop = document.getElementById(
        "start-coloring-btn-top"
      );
      const originalStartButtonBottom = document.getElementById(
        "start-coloring-btn-bottom"
      );
      const originalDownloadBtn = document.getElementById("downloadBtn");
      const originalPrintBtn = document.getElementById("printBtn");
      const originalShareBtn = document.getElementById("shareBtn");

      // --- Clone buttons to remove old listeners ---
      const cloneAndReplace = (btn) => {
        if (!btn) return null;
        const clone = btn.cloneNode(true);
        btn.parentNode?.replaceChild(clone, btn);
        return clone; // Return the fresh clone
      };

      const freshStartButtonTop = cloneAndReplace(originalStartButtonTop);
      const freshStartButtonBottom = cloneAndReplace(originalStartButtonBottom);
      const freshDownloadBtn = cloneAndReplace(originalDownloadBtn);
      const freshPrintBtn = cloneAndReplace(originalPrintBtn);
      const freshShareBtn = cloneAndReplace(originalShareBtn);

      // Re-fetch static image element (might not be strictly necessary but safe)
      const staticImage = document.getElementById("static-coloring-image");

      // --- Static Button Handlers (defined inside init to close over fresh buttons) ---
      function handleStaticDownload() {
        // Ensure we use the passed staticImageUrl
        if (!staticImageUrl) {
          console.error("Static image URL is missing.");
          alert("Could not download image: URL missing.");
          return;
        }
        const link = document.createElement("a");
        link.href = staticImageUrl;
        link.download = `${pageTitle.replace(/\s+/g, "-")}-coloring-page.jpg`;
        link.click();
      }

      function handleStaticPrint() {
        if (!staticImageUrl) {
          console.error("Static image URL is missing for print.");
          alert("Could not print image: URL missing.");
          return;
        }
        const printWindow = window.open(staticImageUrl, "_blank");
        if (printWindow) {
          printWindow.onload = () => {
            printWindow.focus();
            printWindow.print();
          };
        } else {
          alert(
            "Could not open print window. Please allow popups for this site."
          );
        }
      }

      async function handleStaticShare() {
        if (!staticImageUrl) {
          console.error("Static image URL is missing for share.");
          alert("Could not share image: URL missing.");
          return;
        }
        if (navigator.share) {
          try {
            const response = await fetch(staticImageUrl);
            // Check if fetch was successful
            if (!response.ok) {
              throw new Error(`Failed to fetch image: ${response.statusText}`);
            }
            const blob = await response.blob();
            const file = new File([blob], `${pageTitle}-coloring-page.jpg`, {
              type: blob.type,
            });

            if (navigator.canShare && navigator.canShare({ files: [file] })) {
              await navigator.share({
                files: [file],
                title: pageTitle,
                text: `Check out this coloring page: ${pageTitle}`,
                url: window.location.href,
              });
            } else {
              await navigator.share({
                title: pageTitle,
                text: `Check out this coloring page: ${pageTitle}`,
                url: window.location.href,
              });
            }
          } catch (err) {
            console.error("Error sharing static image:", err);
            try {
              // Fallback share URL on error
              await navigator.share({
                title: pageTitle,
                text: `Check out this coloring page: ${pageTitle}`,
                url: window.location.href,
              });
            } catch (shareErr) {
              console.error("Error sharing URL:", shareErr);
              alert("Sharing failed.");
            }
          }
        } else {
          alert("Web Share API not supported on this browser.");
        }
      }

      // --- Attach initial listeners to FRESH buttons ---
      freshDownloadBtn?.addEventListener("click", handleStaticDownload);
      freshPrintBtn?.addEventListener("click", handleStaticPrint);
      freshShareBtn?.addEventListener("click", handleStaticShare);

      // --- Create handler for starting interactive mode ---
      const handleStartColoring = async () => {
        console.log("Start button clicked!");

        // Double-check state before proceeding
        const currentStaticView = document.getElementById(
          "static-coloring-view"
        );
        const currentInteractiveView = document.getElementById(
          "interactive-coloring-view"
        );
        const currentSpinner = document.getElementById("loading-spinner"); // Fetch spinner again

        if (
          currentInteractiveView &&
          !currentInteractiveView.classList.contains("hidden")
        ) {
          console.warn("Interactive view already visible. Aborting.");
          return;
        }
        if (
          !currentStaticView ||
          currentStaticView.classList.contains("hidden")
        ) {
          console.warn("Static view not found or already hidden. Aborting.");
          return;
        }

        currentSpinner?.classList.remove("hidden");
        currentSpinner?.classList.add("flex");
        if (freshStartButtonTop) freshStartButtonTop.disabled = true;
        if (freshStartButtonBottom) freshStartButtonBottom.disabled = true;
        console.log("Spinner shown, buttons disabled.");

        try {
          console.log("Attempting dynamic import...");
          const module = await import(
            "/src/components/coloring-page/coloring-canvas.js"
          );
          console.log("Dynamic import successful.");

          // Hide static view, show interactive view (use potentially updated references)
          currentStaticView?.classList.add("hidden");
          currentInteractiveView?.classList.remove("hidden");
          console.log("Views swapped.");

          // Remove static listeners from the buttons we *know* we added them to
          freshDownloadBtn?.removeEventListener("click", handleStaticDownload);
          freshPrintBtn?.removeEventListener("click", handleStaticPrint);
          freshShareBtn?.removeEventListener("click", handleStaticShare);
          console.log("Static listeners removed.");

          if (module.setupColoringCanvas) {
            console.log("Calling setupColoringCanvas...");
            const pageId =
              typeof location.pathname.split("/").pop() === "string"
                ? location.pathname.split("/").pop()
                : "coloring-page";

            // Pass the potentially updated references and correct vars
            const success = module.setupColoringCanvas(
              "interactive-coloring-view",
              pageId,
              pageTitle,
              staticImageUrl
            );
            console.log("setupColoringCanvas finished. Success:", success);

            if (!success) {
              console.error("Failed to initialize coloring canvas");
              alert(
                "Failed to initialize coloring mode. Please try refreshing the page."
              );
              // Attempt to revert UI changes
              currentStaticView?.classList.remove("hidden");
              currentInteractiveView?.classList.add("hidden");
              if (freshStartButtonTop) freshStartButtonTop.disabled = false;
              if (freshStartButtonBottom)
                freshStartButtonBottom.disabled = false;
              // Re-attach static listeners if needed? Better to rely on refresh/re-nav
            }
          } else {
            console.error("setupColoringCanvas function not found in module.");
            alert("Failed to initialize coloring mode.");
            // Attempt to revert UI changes
            currentStaticView?.classList.remove("hidden");
            currentInteractiveView?.classList.add("hidden");
            if (freshStartButtonTop) freshStartButtonTop.disabled = false;
            if (freshStartButtonBottom) freshStartButtonBottom.disabled = false;
          }
        } catch (error) {
          console.error("Failed to load or initialize coloring module:", error);
          alert(
            "Sorry, could not load the interactive coloring tool. Please try refreshing the page."
          );
          // Attempt to revert UI changes
          currentStaticView?.classList.remove("hidden");
          currentInteractiveView?.classList.add("hidden");
          if (freshStartButtonTop) freshStartButtonTop.disabled = false;
          if (freshStartButtonBottom) freshStartButtonBottom.disabled = false;
        } finally {
          // Hide loading spinner (fetch ref again just in case)
          document.getElementById("loading-spinner")?.classList.remove("flex");
          document.getElementById("loading-spinner")?.classList.add("hidden");
          console.log("Spinner hidden in finally block.");
        }
      };

      // --- Attach listeners to both start buttons ---
      freshStartButtonTop?.addEventListener("click", handleStartColoring);
      freshStartButtonBottom?.addEventListener("click", handleStartColoring);

      console.log("Coloring page script attached listeners.");
    } // End of initializeColoringPage function

    // --- Run Initialization ---
    // Use astro:page-load to ensure this runs on initial load and subsequent client-side navigations
    document.addEventListener("astro:page-load", initializeColoringPage);

    // Optional: Call once immediately if the event might have already fired
    // This can happen if the script loads after DOMContentLoaded and astro:page-load
    if (
      document.readyState === "interactive" ||
      document.readyState === "complete"
    ) {
      if (!window.coloringPageInitialized) {
        // Use a flag to prevent double-init
        initializeColoringPage();
        window.coloringPageInitialized = true;
        // Reset flag when navigating away might be needed if using view transitions heavily,
        // but astro:page-load should handle re-running initializeColoringPage.
      }
    }
  </script>

  <style>
    /* Button style for the coloring buttons */
    .start-coloring-btn {
      opacity: 0.85;
      transition: all 0.2s ease-in-out;
      z-index: 10;
    }

    .start-coloring-btn:hover {
      opacity: 1;
      transform: scale(1.05);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    /* Ensure interactive view takes up space correctly */
    #interactive-coloring-view:not(.hidden) {
      display: flex; /* Or block, depending on ColoringCanvas's root element */
      justify-content: center;
      width: 100%;
    }

    /* Ensure static image respects container width */
    #static-coloring-image {
      max-width: 100%;
      height: auto; /* Maintain aspect ratio */
      display: block; /* Remove extra space below image */
      margin: 0 auto; /* Center if container is wider */
    }

    /* Ensure the main container allows relative positioning */
    .coloring-area-container {
      /* position: relative; */ /* Already added */
      min-height: 400px; /* Give it some minimum height */
    }

    .hidden {
      display: none;
    }

    /* Media query for mobile adjustments */
    @media (max-width: 640px) {
      .start-coloring-btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
      }
    }
  </style>
</Layout>
