---
import Header from "../components/common/Header.astro";
import Footer from "../components/common/Footer.astro";
import { ViewTransitions } from "astro:transitions";
import SEO from "../components/common/SEO.astro";

export interface Props {
  title: string;
  description?: string;
  ogImage?: string;
  canonicalUrl?: string;
}

const {
  title,
  description = "Free printable coloring pages for kids and adults. Browse our collection of categories and find your perfect coloring page.",
  ogImage = "/images/og-default.jpg",
  canonicalUrl,
} = Astro.props;
---

<!doctype html>
<html lang="en" data-theme="coloringTheme">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />

    <SEO
      title={title}
      description={description}
      ogImage={ogImage}
      canonicalUrl={canonicalUrl}
    />

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&family=Lora:wght@400;600&family=Prompt:wght@700&display=swap"
      rel="stylesheet"
    />

    <ViewTransitions />
  </head>
  <body class="min-h-screen flex flex-col bg-base-100">
    <Header />

    <main class="flex-grow">
      <slot />
    </main>

    <Footer />

    <script>
      // Add any global scripts here
      document.addEventListener("astro:page-load", () => {
        // This runs on every page navigation
      });
    </script>
  </body>
</html>

<style is:global>
  /* Global styles */
  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: "Nunito", system-ui, sans-serif;
    line-height: 1.5;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: "Nunito", system-ui, sans-serif;
    line-height: 1.2;
    font-weight: 700;
  }

  p {
    font-family: "Lora", serif;
  }

  /* Animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideUp {
    from {
      transform: translateY(10px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
</style>
