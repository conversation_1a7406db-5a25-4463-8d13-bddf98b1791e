---
export interface Props {
  title: string;
  description: string;
  ogImage?: string;
  canonicalUrl?: string;
  type?: 'website' | 'article';
}

const { 
  title, 
  description, 
  ogImage = "/images/og-default.jpg",
  canonicalUrl,
  type = "website"
} = Astro.props;

const siteUrl = import.meta.env.SITE || "https://coloringpages.example.com";
const pageUrl = canonicalUrl || new URL(Astro.url.pathname, siteUrl).toString();
const ogImageUrl = new URL(ogImage, siteUrl).toString();
---

<!-- Primary Meta Tags -->
<title>{title}</title>
<meta name="title" content={title} />
<meta name="description" content={description} />

<!-- Canonical URL -->
<link rel="canonical" href={pageUrl} />

<!-- Open Graph / Facebook -->
<meta property="og:type" content={type} />
<meta property="og:url" content={pageUrl} />
<meta property="og:title" content={title} />
<meta property="og:description" content={description} />
<meta property="og:image" content={ogImageUrl} />

<!-- Twitter -->
<meta property="twitter:card" content="summary_large_image" />
<meta property="twitter:url" content={pageUrl} />
<meta property="twitter:title" content={title} />
<meta property="twitter:description" content={description} />
<meta property="twitter:image" content={ogImageUrl} />

<!-- Additional SEO Meta Tags -->
<meta name="robots" content="index, follow" />
<meta name="author" content="Coloring Pages" />