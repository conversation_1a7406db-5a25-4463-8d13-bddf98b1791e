import os
import csv
import shutil
import argparse
import re
import time
import sys

# --- CONFIGURATION CONSTANTS ---
# File/Folder Paths
SRC_DIR = 'src'
SD_MODELS_DIR = 'sd'
CATEGORIES_CSV = 'categories.csv'
PAGES_CSV = 'pages.csv'

# Stable Diffusion Model Paths
BASE_MODEL_PATH = os.path.join(SD_MODELS_DIR, 'sd_xl_base_1.0.safetensors')
VAE_PATH = os.path.join(SD_MODELS_DIR, 'sdxl_vae.safetensors')
LORA_PATH = os.path.join(SD_MODELS_DIR, 'ColoringBookRedmond-ColoringBook-ColoringBookAF.safetensors')

# Default Stable Diffusion Settings
SD_DEFAULTS = {
    'steps': 25,
    'guidance': 7.0,
    'lora_scale': 1.2,
}

# Image Orientation Resolutions
ORIENTATIONS = {
    'landscape': (1216, 832),
    'portrait': (832, 1216),
    'square': (1024, 1024),
}

# Prompt Templates
POSITIVE_PROMPT_TEMPLATE = "coloring page of {subject}, {camera_angle}, {camera_shot}, coloring book style, line art, thin lines, clean outlines, clean vector style, minimalist, simple background, black and white"
NEGATIVE_PROMPT = "color, shading, gradient, shadows, shades, transparency, noisy, blurry, watermark, text, details, background details, complex background, cropped, scanlines, tramlines, hatch lines, parallel lines, extra lines, thick lines, crosshatching, dither, texture, noise, artifacts, ornaments, scribble, gray, grey"

# --- DATA LOADING FUNCTIONS ---
def load_data_from_csv(filepath):
    parent_categories = {}
    subcategories_to_process = []
    try:
        with open(filepath, mode='r', newline='', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                if not row['parent-category']:
                    category_slug = row['category']
                    parent_categories[category_slug] = {
                        "slug": category_slug, "name": row['display-name'], "description": row['description'],
                        "related": row['related-categories'].split(',') if row['related-categories'] else [],
                        "content": row['content'], "subcategories": []
                    }
                else:
                    subcategories_to_process.append(row)
    except FileNotFoundError:
        print(f"Error: The file '{filepath}' was not found.")
        return None
    for sub_row in subcategories_to_process:
        parent_slug = sub_row['parent-category']
        if parent_slug in parent_categories:
            parent_categories[parent_slug]['subcategories'].append({
                "slug": sub_row['category'], "name": sub_row['display-name'], "description": sub_row['description'],
                "related": sub_row['related-categories'].split(',') if sub_row['related-categories'] else [],
                "content": sub_row['content']
            })
        else:
            print(f"Warning: Parent category '{parent_slug}' not found for subcategory '{sub_row['category']}'.")
    return list(parent_categories.values())

def load_pages_data_from_csv(filepath):
    pages = []
    try:
        with open(filepath, mode='r', newline='', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                pages.append(row)
        return pages
    except FileNotFoundError:
        print(f"Error: The file '{filepath}' was not found.")
        return None

# --- FILE & DIRECTORY SYNC FUNCTIONS ---
def sync_category_markdown(base_path, categories):
    print(f"\n--- Syncing Category Markdown Structure in '{base_path}' ---")
    os.makedirs(base_path, exist_ok=True)
    for category in categories:
        os.makedirs(os.path.join(base_path, category["slug"]), exist_ok=True)
        parent_file_path = os.path.join(base_path, f'{category["slug"]}.md')
        related_string = "[" + ", ".join([f'"{s}"' for s in category.get("related", []) if s]) + "]"
        with open(parent_file_path, 'w', encoding='utf-8') as f:
            f.write(f'---\nname: "{category["name"]}"\ndescription: "{category["description"]}"\nrelatedCategories: {related_string}\n---\n{category["content"]}\n')
        for subcategory in category["subcategories"]:
            sub_file_path = os.path.join(base_path, category["slug"], f'{subcategory["slug"]}.md')
            related_string = "[" + ", ".join([f'"{s}"' for s in subcategory.get("related", []) if s]) + "]"
            with open(sub_file_path, 'w', encoding='utf-8') as f:
                f.write(f'---\nname: "{subcategory["name"]}"\ndescription: "{subcategory["description"]}"\nrelatedCategories: {related_string}\n---\n{subcategory["content"]}\n')

def sync_page_markdown(base_path, pages_data):
    print(f"\n--- Syncing Coloring Page Markdown Files in '{base_path}' ---")
    if not pages_data: return
    for page in pages_data:
        page_dir = os.path.join(base_path, page['parent-category'], page['category'])
        os.makedirs(page_dir, exist_ok=True)
        file_path = os.path.join(page_dir, f"{page['slug']}.md")
        title = page['title'].replace('"', '\\"')
        description = page['description'].replace('"', '\\"')
        subject = page['subject'].replace('"', '\\"')
        yaml_content = f"""---
title: "{title}"
description: "{description}"
difficulty: "{page['difficulty']}"
subject: "{subject}"
orientation: "{page['orientation']}"
---
{page['content']}
"""
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(yaml_content)

def sync_directory_structure(base_path, categories):
    print(f"\n--- Syncing Directory Structure in '{base_path}' ---")
    os.makedirs(base_path, exist_ok=True)
    for category in categories:
        os.makedirs(os.path.join(base_path, category['slug']), exist_ok=True)
        for subcategory in category['subcategories']:
            os.makedirs(os.path.join(base_path, category['slug'], subcategory['slug']), exist_ok=True)

def get_expected_paths(categories, pages, md_base, coloring_pages_base):
    expected_md, expected_coloring = set(), set()
    for cat in categories:
        expected_md.add(os.path.normpath(os.path.join(md_base, f"{cat['slug']}.md")))
        for base_path, path_set in [(md_base, expected_md), (coloring_pages_base, expected_coloring)]:
            path_set.add(os.path.normpath(os.path.join(base_path, cat['slug'])))
        for sub in cat['subcategories']:
            expected_md.add(os.path.normpath(os.path.join(md_base, cat['slug'], f"{sub['slug']}.md")))
            path_set = expected_coloring
            path_set.add(os.path.normpath(os.path.join(base_path, cat['slug'], sub['slug'])))
    if pages:
        for page in pages:
            page_path = os.path.join(coloring_pages_base, page['parent-category'], page['category'], f"{page['slug']}.md")
            expected_coloring.add(os.path.normpath(page_path))
    return expected_md, expected_coloring

def cleanup_orphans(base_path, expected_paths):
    print(f"\n--- Cleaning orphaned files/folders in '{base_path}' ---")
    if not os.path.exists(base_path): return
    for root, dirs, files in os.walk(base_path, topdown=False):
        for name in files:
            file_path = os.path.normpath(os.path.join(root, name))
            if file_path not in expected_paths:
                print(f"Removing orphaned file: {file_path}")
                os.remove(file_path)
        for name in dirs:
            dir_path = os.path.normpath(os.path.join(root, name))
            if dir_path not in expected_paths:
                print(f"Removing orphaned directory: {dir_path}")
                shutil.rmtree(dir_path)

def cleanup_orphan_images(base_path, valid_page_slugs):
    print(f"\n--- Cleaning orphaned images in '{base_path}' ---")
    if not os.path.exists(base_path): return
    slug_extractor = re.compile(r"^(.*?)(-\d+)?\.png$")
    for root, dirs, files in os.walk(base_path, topdown=False):
        for name in files:
            if not name.endswith('.png'): continue
            file_path = os.path.join(root, name)
            match = slug_extractor.match(name)
            if match:
                base_slug = match.group(1)
                if base_slug not in valid_page_slugs:
                    print(f"Removing orphaned image: {file_path}")
                    os.remove(file_path)
            else:
                print(f"Removing non-conforming file: {file_path}")
                os.remove(file_path)
        if os.path.exists(root) and not os.listdir(root):
            print(f"Removing empty image directory: {root}")
            os.rmdir(root)

# --- IMAGE GENERATION FUNCTION ---
def generate_images(pages_data, variants=1, steps=25, guidance=7.0, lora_scale=1.0):
    import torch
    from diffusers import AutoencoderKL, StableDiffusionXLPipeline, EulerAncestralDiscreteScheduler

    device = "cuda" if torch.cuda.is_available() else "cpu"
    if device == "cpu":
        print("CUDA is not available. Image generation requires a GPU.")
        return

    print("\n--- Initializing Stable Diffusion Pipeline (this may take a moment) ---")
    try:
        torch_dtype = torch.float16
        vae = AutoencoderKL.from_single_file(VAE_PATH, torch_dtype=torch_dtype)
        pipe = StableDiffusionXLPipeline.from_single_file(
            BASE_MODEL_PATH, vae=vae, torch_dtype=torch_dtype, use_safetensors=True
        )
        pipe.scheduler = EulerAncestralDiscreteScheduler.from_config(pipe.scheduler.config)
        pipe.to(device)
        pipe.enable_model_cpu_offload()
        pipe.load_lora_weights(LORA_PATH)
        print("Pipeline initialized successfully.")
    except Exception as e:
        print(f"Error initializing Diffusion Pipeline: {e}")
        return

    image_base_path = os.path.join(SRC_DIR, 'assets', 'images')
    print(f"\n--- Starting Image Generation (Variants per page: {variants}) ---")
    
    start_time = time.perf_counter()
    generated_count = 0

    for i, page in enumerate(pages_data):
        print(f"\nProcessing page {i+1}/{len(pages_data)}: {page['slug']}")
        target_dir = os.path.join(image_base_path, page['parent-category'], page['category'])
        os.makedirs(target_dir, exist_ok=True)
        base_image_path = os.path.join(target_dir, f"{page['slug']}.png")
        if os.path.exists(base_image_path):
            print(f"  -> Skipping: Base image already exists.")
            continue
        
        generated_count += 1
        positive_prompt = POSITIVE_PROMPT_TEMPLATE.format(
            subject=page['subject'], camera_angle=page['camera-angle'], camera_shot=page['camera-shot']
        )
        width, height = ORIENTATIONS.get(page.get('orientation', 'portrait').lower(), ORIENTATIONS['portrait'])
        
        print(f"  -> Generating {variants} variant(s) for '{page['slug']}' at {width}x{height}...")
        images = pipe(
            prompt=positive_prompt, negative_prompt=NEGATIVE_PROMPT, num_inference_steps=steps,
            guidance_scale=guidance, cross_attention_kwargs={"scale": lora_scale},
            width=width, height=height, num_images_per_prompt=variants, add_watermarker=False
        ).images
        
        for idx, img in enumerate(images):
            save_path = base_image_path if idx == 0 else os.path.join(target_dir, f"{page['slug']}-{idx + 1}.png")
            img.save(save_path)
            print(f"  -> Saved: {save_path}")

    end_time = time.perf_counter()
    duration = end_time - start_time
    
    if generated_count > 0:
        minutes, seconds = divmod(duration, 60)
        print("\n--- Image Generation Complete ---")
        print(f"Generated {generated_count * variants} image(s) for {generated_count} page(s).")
        print(f"Total time taken: {int(minutes)} minutes and {seconds:.2f} seconds.")
    else:
        print("\n--- No new images to generate. All pages are up to date. ---")


# --- MAIN EXECUTION BLOCK ---
if __name__ == "__main__":
    # Get just the script name for cleaner help text
    script_name = os.path.basename(sys.argv[0])

    # Define a detailed epilog for the help message
    help_epilog = f"""
Examples:

  1. Synchronize all content files and folders with the CSVs:
     python {script_name} content

  2. Generate missing images with default settings:
     python {script_name} images

  3. Generate 3 variants for each missing image:
     python {script_name} images --variants 3

  4. Generate missing images with custom settings:
     python {script_name} images --steps 30 --guidance 6.5 --lora 0.9
"""

    parser = argparse.ArgumentParser(
        description="A tool to synchronize content and generate images for the coloring page site.",
        formatter_class=argparse.RawTextHelpFormatter,
        epilog=help_epilog
    )
    subparsers = parser.add_subparsers(dest="command", help="Available commands")

    content_parser = subparsers.add_parser("content", help="Synchronize folders and markdown files based on CSVs.")
    images_parser = subparsers.add_parser("images", help="Generate coloring page images for missing files.")
    images_parser.add_argument("--variants", type=int, default=1, help="Number of image variants to generate for each missing page.")
    images_parser.add_argument("--steps", type=int, default=SD_DEFAULTS['steps'], help=f"Number of inference steps (default: {SD_DEFAULTS['steps']}).")
    images_parser.add_argument("--guidance", type=float, default=SD_DEFAULTS['guidance'], help=f"Guidance scale (default: {SD_DEFAULTS['guidance']}).")
    images_parser.add_argument("--lora", type=float, default=SD_DEFAULTS['lora_scale'], dest='lora_scale', help=f"LoRA scale/weight (default: {SD_DEFAULTS['lora_scale']}).")

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        sys.exit(0)

    category_data = load_data_from_csv(CATEGORIES_CSV)
    pages_data = load_pages_data_from_csv(PAGES_CSV)

    if args.command == "content":
        print("Running in Content Sync mode.")
        if category_data:
            md_path = os.path.join(SRC_DIR, 'content', 'categories')
            coloring_pages_path = os.path.join(SRC_DIR, 'content', 'coloring-pages')
            image_path = os.path.join(SRC_DIR, 'assets', 'images')
            expected_md, expected_coloring = get_expected_paths(category_data, pages_data, md_path, coloring_pages_path)
            valid_page_slugs = {p['slug'] for p in pages_data} if pages_data else set()
            cleanup_orphans(md_path, expected_md)
            cleanup_orphans(coloring_pages_path, expected_coloring)
            cleanup_orphan_images(image_path, valid_page_slugs)
            sync_category_markdown(md_path, category_data)
            sync_directory_structure(coloring_pages_path, category_data)
            sync_directory_structure(image_path, category_data)
            if pages_data:
                sync_page_markdown(coloring_pages_path, pages_data)
            print("\nSynchronization complete! Filesystem is now in sync with CSV data.")
        else:
            print("Could not load category data. Aborting script.")

    elif args.command == "images":
        print("Running in Image Generation mode.")
        if pages_data:
            generate_images(
                pages_data,
                variants=args.variants,
                steps=args.steps,
                guidance=args.guidance,
                lora_scale=args.lora_scale
            )
        else:
            print("Could not load pages data. Aborting image generation.")