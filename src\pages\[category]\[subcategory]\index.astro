---
import Layout from "../../../layouts/Layout.astro";
import { Image } from "astro:assets";
import ColoringPageCard from "../../../components/common/ColoringPageCard.astro";
import {
  getAllCategories,
  getCategoryBySlug,
  getColoringPagesByCategory,
  getDisplayName,
  getColoringPageThumbnailPath,
} from "../../../utils/collections";

export async function getStaticPaths() {
  // Get all subcategories (categories with a slash in their slug)
  const allCategories = await getAllCategories();
  const subcategories = allCategories.filter((cat) => cat.slug.includes("/"));

  // Generate paths for each subcategory
  return subcategories.map((subcat) => {
    // Split the slug to get parent category and subcategory
    const slugParts = subcat.slug.split("/");
    const parentCategorySlug = slugParts[0];
    const subcategorySlug = slugParts[slugParts.length - 1];

    return {
      params: {
        category: parentCategorySlug,
        subcategory: subcategorySlug,
      },
      props: {
        parentCategorySlug: parentCategorySlug,
        subcategorySlug: subcategorySlug,
        subcategoryEntry: subcat,
      },
    };
  });
}

const { parentCategorySlug, subcategorySlug, subcategoryEntry } = Astro.props;

// Get subcategory details
const subcategoryName = getDisplayName(subcategoryEntry);
const description = subcategoryEntry.data.description;

// Get parent category details
const parentCategoryName = parentCategorySlug
  ? getDisplayName(parentCategorySlug)
  : "";
const parentCategoryEntry = parentCategorySlug
  ? await getCategoryBySlug(parentCategorySlug)
  : undefined;

// Get coloring pages for this subcategory
const coloringPages = await getColoringPagesByCategory(
  parentCategorySlug || "",
  subcategorySlug || ""
);
---

<Layout title={`${subcategoryName} Coloring Pages`} description={description}>
  <div class="container mx-auto px-4 py-8">
    <div class="text-sm breadcrumbs mb-6">
      <ul>
        <li><a href="/">Home</a></li>
        {
          parentCategoryEntry && (
            <li>
              <a href={`/${parentCategoryEntry.slug}/`}>
                {getDisplayName(parentCategoryEntry)}
              </a>
            </li>
          )
        }
        <li>{subcategoryName}</li>
      </ul>
    </div>
    <header class="mb-10 text-center md:text-left">
      <h1 class="text-3xl md:text-4xl font-bold mb-4">
        {subcategoryName} Coloring Pages
      </h1>
      <p class="text-lg text-neutral-600 max-w-3xl">{description}</p>
    </header>
    <section>
      {
        coloringPages.length > 0 ? (
          <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {coloringPages.map((page) => {
              const fullSlug = page.slug;
              const slug = fullSlug.includes("/")
                ? fullSlug.split("/").pop()
                : fullSlug;
              const thumb = getColoringPageThumbnailPath(page);
              return (
                <ColoringPageCard
                  id={slug || ""}
                  title={getDisplayName(page)}
                  description={page.data.description}
                  thumbnail={thumb}
                  categoryId={parentCategorySlug || ""}
                  subcategoryId={subcategorySlug}
                  difficulty={page.data.difficulty}
                  slug={slug || ""}
                />
              );
            })}
          </div>
        ) : (
          <div class="text-center py-10">
            <p class="text-neutral-600">
              No coloring pages found in this subcategory yet. Check back soon!
            </p>
          </div>
        )
      }
    </section>
  </div>
</Layout>
