---
import {
  getAllCategories,
  getDisplayName,
  getCategoryUri,
} from "../../utils/collections";

const allCategories = await getAllCategories();
const popularCategories = allCategories.slice(0, 6);
const currentYear = new Date().getFullYear();
---

<footer class="bg-neutral text-neutral-content mt-16">
  <div class="container mx-auto px-4 pt-16 pb-8">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
      <div>
        <h3 class="text-lg font-bold mb-4">ColoringMix</h3>
        <p class="text-sm opacity-80 mb-4">
          Free printable coloring pages for kids and adults. Download, print,
          and start coloring today!
        </p>
        <div class="flex space-x-4">
          <a href="#" class="hover:text-primary transition-colors">
            <span class="sr-only">Facebook</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="currentColor"
              class="w-5 h-5">
              <path
                d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"
              ></path>
            </svg>
          </a>
          <a href="#" class="hover:text-primary transition-colors">
            <span class="sr-only">Instagram</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="currentColor"
              class="w-5 h-5">
              <path
                d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"
              ></path>
            </svg>
          </a>
          <a href="#" class="hover:text-primary transition-colors">
            <span class="sr-only">Pinterest</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="currentColor"
              class="w-5 h-5">
              <path
                d="M12 0c-6.627 0-12 5.372-12 12 0 5.084 3.163 9.426 7.627 11.174-.105-.949-.2-2.405.042-3.441.218-.937 1.407-5.965 1.407-5.965s-.359-.719-.359-1.782c0-1.668.967-2.914 2.171-2.914 1.023 0 1.518.769 1.518 1.69 0 1.029-.655 2.568-.994 3.995-.283 1.194.599 2.169 1.777 2.169 2.133 0 3.772-2.249 3.772-5.495 0-2.873-2.064-4.882-5.012-4.882-3.414 0-5.418 2.561-5.418 5.207 0 1.031.397 2.138.893 2.738.098.119.112.224.083.345l-.333 1.36c-.053.22-.174.267-.402.161-1.499-.698-2.436-2.889-2.436-4.649 0-3.785 2.75-7.262 7.929-7.262 4.163 0 7.398 2.967 7.398 6.931 0 4.136-2.607 7.464-6.227 7.464-1.216 0-2.359-.631-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146 1.124.347 2.317.535 3.554.535 6.627 0 12-5.373 12-12 0-6.628-5.373-12-12-12z"
              ></path>
            </svg>
          </a>
        </div>
      </div>

      <div>
        <h3 class="footer-title">Popular Categories</h3>
        <ul class="space-y-2">
          {
            popularCategories.map((category) => (
              <li>
                <a
                  href={getCategoryUri(category)}
                  class="hover:text-primary-300 transition-colors">
                  {getDisplayName(category)}
                </a>
              </li>
            ))
          }
        </ul>
      </div>

      <div>
        <h3 class="footer-title">Site Links</h3>
        <ul class="space-y-2">
          <li>
            <a href="/about/" class="hover:text-primary-300 transition-colors"
              >About</a
            >
          </li>
          <li>
            <a href="/contact/" class="hover:text-primary-300 transition-colors"
              >Contact</a
            >
          </li>
          <li>
            <a href="/privacy/" class="hover:text-primary-300 transition-colors"
              >Privacy Policy</a
            >
          </li>
          <li>
            <a href="/terms/" class="hover:text-primary-300 transition-colors"
              >Terms of Service</a
            >
          </li>
        </ul>
      </div>

      <div>
        <h3 class="footer-title">Newsletter</h3>
        <p class="text-sm opacity-80 mb-4">
          Get updates on new coloring pages and special offers.
        </p>
        <div class="form-control w-full">
          <div class="relative">
            <input
              type="email"
              placeholder="Your email"
              class="input input-bordered w-full pr-16"
            />
            <button
              class="btn btn-primary absolute top-0 right-0 rounded-l-none"
              >Subscribe</button
            >
          </div>
        </div>
      </div>
    </div>

    <div class="divider my-8 opacity-20"></div>

    <div class="text-center text-sm opacity-70">
      <p>&copy; {currentYear} ColoringMix. All rights reserved.</p>
    </div>
  </div>
</footer>
