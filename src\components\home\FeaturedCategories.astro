---
import CategoryCard from "../common/CategoryCard.astro";
import {
  getMainCategories,
  getFeaturedCategories,
  getDisplayName,
  getCategoryThumbnailPath,
} from "../../utils/collections";

interface Props {
  limit?: number;
}

const { limit = 4 } = Astro.props;

// Get featured categories first
const featuredCategories = await getFeaturedCategories();

// If we don't have enough featured categories, add non-featured main categories
let displayCategories = featuredCategories;

if (featuredCategories.length < limit) {
  const mainCategories = await getMainCategories();
  const nonFeaturedCategories = mainCategories.filter(
    (category) => !featuredCategories.some((fc) => fc.slug === category.slug)
  );

  // Add non-featured categories up to the limit
  displayCategories = [
    ...featuredCategories,
    ...nonFeaturedCategories.slice(0, limit - featuredCategories.length),
  ];
}

// Final limit to ensure we don't exceed the requested number
displayCategories = displayCategories.slice(0, limit);
---

<section class="py-12 bg-base-100">
  <div class="container mx-auto px-4">
    <div class="text-center mb-10">
      <h2 class="text-3xl md:text-4xl font-bold mb-2">Featured Categories</h2>
      <p class="text-neutral-600 max-w-2xl mx-auto">
        Browse our collection of free printable coloring pages organized by main
        categories. Find the perfect design to color!
      </p>
    </div>

    <div
      class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
      {
        displayCategories.map((category, idx) => (
          <div
            class="animate-fade-in"
            style={`animation-delay: ${idx * 100}ms`}>
            <CategoryCard
              id={category.slug}
              name={getDisplayName(category)}
              description={category.data.description}
              thumbnail={getCategoryThumbnailPath(category)}
              slug={category.slug}
            />
          </div>
        ))
      }
    </div>

    <div class="text-center mt-10">
      <a href="/categories/" class="btn btn-primary">
        View All Categories
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 ml-1"
          viewBox="0 0 20 20"
          fill="currentColor">
          <path
            fill-rule="evenodd"
            d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
            clip-rule="evenodd"></path>
        </svg>
      </a>
    </div>
  </div>
</section>
