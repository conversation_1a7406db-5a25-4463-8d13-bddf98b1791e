---
import CategoryCard from "../common/CategoryCard.astro";
import {
  getCategoryBySlug,
  getAllCategories,
  getDisplayName,
  getCategoryThumbnailPath,
} from "../../utils/collections";

interface Props {
  categorySlug: string;
}

const { categorySlug } = Astro.props;
const category = await getCategoryBySlug(categorySlug);
const allCategories = await getAllCategories();
const relatedSlugs = category?.data?.relatedCategories || [];
const relatedCategories = allCategories
  .filter((cat) => relatedSlugs.includes(cat.slug))
  .slice(0, 4);
---

{
  relatedCategories.length > 0 && (
    <section class="mt-16">
      <h2 class="text-2xl font-bold mb-6">Related Categories</h2>
      <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
        {relatedCategories.map((category) => (
          <CategoryCard
            id={category.slug}
            name={getDisplayName(category)}
            description={category.data.description}
            thumbnail={getCategoryThumbnailPath(category)}
            slug={category.slug}
          />
        ))}
      </div>
    </section>
  )
}
