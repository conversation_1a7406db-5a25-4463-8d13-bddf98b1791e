---
import Layout from "../../layouts/Layout.astro";
import { Image } from "astro:assets";
import {
  getAllCategories,
  getCategoryBySlug,
  getSubcategories,
  getColoringPagesByCategory,
  getDisplayName,
  getCategoryThumbnailPath,
  getColoringPageThumbnailPath,
} from "../../utils/collections";

export async function getStaticPaths() {
  const categories = await getAllCategories();
  return categories.map((cat) => ({
    params: { category: cat.slug },
    props: { categoryEntry: cat },
  }));
}

const { categoryEntry } = Astro.props;

const categorySlug = categoryEntry.slug;
const categoryName = getDisplayName(categoryEntry);
const description =
  categoryEntry?.data?.description ||
  `Browse our ${categoryName} coloring pages.`;

// Check if this is a main category (no slash in slug)
const isMainCategory = !categorySlug.includes("/");

// Get subcategories if this is a main category
const subcategories = isMainCategory
  ? await getSubcategories(categorySlug)
  : [];

// Get coloring pages if this is a subcategory
const coloringPages = !isMainCategory
  ? await getColoringPagesByCategory(
      categorySlug.split("/")[0] || "",
      categorySlug.split("/")[1] || ""
    )
  : [];

// Fetch the parent category entry if this is a subcategory page
const parentCategoryEntry = !isMainCategory
  ? await getCategoryBySlug(categorySlug.split("/")[0] || "")
  : null;
---

<Layout title={`${categoryName} Coloring Pages`} description={description}>
  <div class="container mx-auto px-4 py-8">
    <div class="text-sm breadcrumbs mb-6">
      <ul>
        <li><a href="/">Home</a></li>
        {
          !isMainCategory && parentCategoryEntry && (
            <li>
              <a href={`/${parentCategoryEntry.slug}/`}>
                {getDisplayName(parentCategoryEntry)}
              </a>
            </li>
          )
        }
        <li>{categoryName}</li>
      </ul>
    </div>
    <header class="mb-10 text-center md:text-left">
      <h1 class="text-3xl md:text-4xl font-bold mb-4">
        {categoryName}
        {isMainCategory ? "Categories" : "Coloring Pages"}
      </h1>
      <p class="text-lg text-neutral-600 max-w-3xl">{description}</p>
    </header>

    {/* Show subcategories for main categories */}
    {
      isMainCategory && (
        <section>
          {subcategories.length > 0 ? (
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {subcategories.map((subcat) => {
                // Extract just the subcategory name without parent path if needed
                const cleanSubcatSlug = subcat.slug.includes("/")
                  ? subcat.slug.split("/").pop() || ""
                  : subcat.slug;

                const subcatThumb = getCategoryThumbnailPath(subcat);

                return (
                  <a
                    href={`/${categorySlug}/${cleanSubcatSlug}/`}
                    class="block border rounded shadow hover:shadow-lg transition overflow-hidden bg-white">
                    <Image
                      src={subcatThumb}
                      alt={getDisplayName(subcat)}
                      width={300}
                      height={400}
                    />
                    <div class="p-4">
                      <h2 class="font-semibold text-lg mb-2">
                        {getDisplayName(subcat)}
                      </h2>
                      <p class="text-sm text-neutral-600">
                        {subcat.data.description}
                      </p>
                    </div>
                  </a>
                );
              })}
            </div>
          ) : (
            <div class="text-center py-10">
              <p class="text-neutral-600">
                No subcategories found for this category yet. Check back soon!
              </p>
            </div>
          )}
        </section>
      )
    }

    {/* Show coloring pages for subcategories */}
    {
      !isMainCategory && (
        <section>
          {coloringPages.length > 0 ? (
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {coloringPages.map((page) => {
                const fullSlug = page.slug;
                const slug = fullSlug.includes("/")
                  ? fullSlug.split("/").pop()
                  : fullSlug;

                // Get parent category and subcategory from the slug path
                const pathSegments = page.slug.split("/");
                const pageCategory =
                  pathSegments.length > 0 ? pathSegments[0] : "";
                const pageSubcategory =
                  pathSegments.length > 1 ? pathSegments[1] : "";

                const thumb = getColoringPageThumbnailPath(page);

                // Add "-coloring-page" suffix to the slug for URL paths
                const urlSlug = `${typeof slug === "string" ? slug : ""}-coloring-page`;

                return (
                  <a
                    href={`/${pageCategory}/${pageSubcategory}/${urlSlug}/`}
                    class="block border rounded shadow hover:shadow-lg transition overflow-hidden bg-white">
                    <Image
                      src={thumb}
                      alt={getDisplayName(page)}
                      width={300}
                      height={400}
                    />
                    <div class="p-4">
                      <h2 class="font-semibold text-lg mb-2">
                        {getDisplayName(page)}
                      </h2>
                      <p class="text-sm text-neutral-600">
                        {page.data.description}
                      </p>
                      <div class="mt-2 text-xs text-neutral-500">
                        Difficulty: {page.data.difficulty}
                      </div>
                      {page.data.featured && (
                        <span class="inline-block bg-yellow-200 text-yellow-800 px-2 py-1 rounded text-xs font-bold mt-2">
                          Featured
                        </span>
                      )}
                    </div>
                  </a>
                );
              })}
            </div>
          ) : (
            <div class="text-center py-10">
              <p class="text-neutral-600">
                No coloring pages found in this subcategory yet. Check back
                soon!
              </p>
            </div>
          )}
        </section>
      )
    }
  </div>
</Layout>
