---
import Layout from "../../layouts/Layout.astro";
import CategoryCard from "../../components/common/CategoryCard.astro";
import {
  getAllCategories,
  getMainCategories,
  getSubcategories,
  getDisplayName,
  getCategoryImagePath,
  getCategoryThumbnailPath
} from "../../utils/collections";
import type { CategoryEntry } from "../../utils/collections";

// Fetch all categories and main categories
const allCategories = await getAllCategories();
const mainCategories = await getMainCategories();

// Sort main categories alphabetically by slug
mainCategories.sort((a, b) => a.slug.localeCompare(b.slug));

// Create a map of main categories to their subcategories
const categoryMap = new Map<CategoryEntry, CategoryEntry[]>();

for (const mainCat of mainCategories) {
  const subcategories = await getSubcategories(mainCat.slug);

  // Sort subcategories alphabetically
  subcategories.sort((a, b) => {
    const aSlug = a.slug.includes("/") ? a.slug.split("/").pop() || "" : a.slug;
    const bSlug = b.slug.includes("/") ? b.slug.split("/").pop() || "" : b.slug;
    return aSlug.localeCompare(bSlug);
  });

  categoryMap.set(mainCat, subcategories);
}

// Extract just the subcategory name without parent path
function getSubcategoryName(slug: string): string {
  const cleanSubcatSlug = slug.includes("/")
    ? slug.split("/").pop() || ""
    : slug;
  return getDisplayName(cleanSubcatSlug);
}

// Create anchor links for navigation
const anchorLinks = Array.from(categoryMap.keys()).map((category) => ({
  id: category.slug,
  name: getDisplayName(category),
}));
---

<Layout
  title="Browse All Coloring Page Categories"
  description="Explore our complete collection of free printable coloring pages organized by categories. Find the perfect design for kids and adults of all ages and skill levels."
>
  <div class="container mx-auto px-4 py-8">
    <div class="text-sm breadcrumbs mb-6">
      <ul>
        <li><a href="/">Home</a></li>
        <li>All Categories</li>
      </ul>
    </div>

    <header class="mb-12 text-center">
      <h1 class="text-3xl md:text-4xl font-bold mb-4">
        Browse All Coloring Page Categories
      </h1>
      <p class="text-lg text-neutral-600 max-w-3xl mx-auto">
        Explore our complete collection of free printable coloring pages organized by categories.
        Find designs for all ages and skill levels, from simple outlines for kids to complex patterns for adults.
      </p>
    </header>

    <!-- Quick Navigation -->
    <nav class="mb-16 p-4 bg-base-200 rounded-lg shadow-sm">
      <h2 class="text-lg font-bold mb-2">Quick Navigation</h2>
      <div class="flex flex-wrap gap-2">
        {anchorLinks.map((link) => (
          <a
            href={`#${link.id}`}
            class="btn btn-sm btn-outline btn-primary normal-case"
          >
            {link.name}
          </a>
        ))}
      </div>
    </nav>

    <div class="h-16"></div> <!-- Empty spacer div -->

    <!-- Categories and Subcategories -->
    {Array.from(categoryMap.entries()).map(([mainCategory, subcategories]) => (
      <section id={mainCategory.slug} class="mb-24 scroll-mt-16">
        <div class="border-b pb-2 mb-8">
          <h2 class="text-2xl md:text-3xl font-bold">
            {getDisplayName(mainCategory)}
            Coloring Pages
          </h2>
        </div>

        <!-- Main Category Card -->
        <div class="mb-12">
          <a
            href={`/${mainCategory.slug}/`}
            class="block p-6 bg-base-100 shadow-md hover:shadow-lg transition-all duration-300 rounded-lg"
          >
            <div class="md:flex items-center gap-6">
              <div class="md:w-1/3 mb-4 md:mb-0">
                <div class="aspect-[4/3] overflow-hidden rounded-lg">
                  <img
                    src={getCategoryImagePath(mainCategory)}
                    alt={`${getDisplayName(mainCategory)} coloring pages category`}
                    class="w-full h-full object-cover"
                  />
                </div>
              </div>
              <div class="md:w-2/3">
                <h3 class="text-xl font-bold mb-2">All {getDisplayName(mainCategory)} Coloring Pages</h3>
                <p class="text-neutral-600 mb-4">{mainCategory.data.description}</p>
                <span class="text-primary-600 font-semibold inline-flex items-center group-hover:translate-x-1 transition-transform">
                  Browse All {getDisplayName(mainCategory)} Pages
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4 ml-1"
                    viewBox="0 0 20 20"
                    fill="currentColor">
                    <path
                      fill-rule="evenodd"
                      d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"></path>
                  </svg>
                </span>
              </div>
            </div>
          </a>
        </div>

        <!-- Subcategories -->
        {subcategories.length > 0 && (
          <div class="mb-8">
            <h3 class="text-xl font-semibold mb-6">{getDisplayName(mainCategory)} Subcategories</h3>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {subcategories.map((subcat) => {
                // Extract the subcategory name from the slug
                // const subcatName = getSubcategoryName(subcat.slug);

                return (
                  <CategoryCard
                    id={subcat.slug}
                    name={getDisplayName(subcat)}
                    description={subcat.data.description}
                    thumbnail={getCategoryThumbnailPath(subcat)}
                    slug={subcat.slug}
                  />
                );
              })}
            </div>
          </div>
        )}
      </section>
    ))}
  </div>
</Layout>

<style>
  /* Smooth scrolling for anchor links */
  html {
    scroll-behavior: smooth;
  }

  /* Add some padding to account for fixed header when scrolling to anchor */
  section[id] {
    scroll-margin-top: 2rem;
  }
</style>