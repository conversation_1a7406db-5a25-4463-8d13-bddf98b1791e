---
// How it works section for homepage
---

<section class="py-16 bg-base-100">
  <div class="container mx-auto px-4">
    <div class="text-center mb-12">
      <h2 class="text-3xl md:text-4xl font-bold mb-2">How It Works</h2>
      <p class="text-neutral-600 max-w-2xl mx-auto">
        Create beautiful colored art in three simple steps
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <div
        class="card bg-base-100 border border-base-300 shadow-sm hover:shadow-md transition-shadow">
        <div class="card-body items-center text-center">
          <div
            class="w-16 h-16 rounded-full bg-primary-100 flex items-center justify-center mb-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-8 w-8 text-primary-600"
              viewBox="0 0 20 20"
              fill="currentColor">
              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
              <path
                fill-rule="evenodd"
                d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                clip-rule="evenodd"></path>
            </svg>
          </div>
          <h3 class="text-xl font-bold mb-2">1. Browse & Choose</h3>
          <p class="text-neutral-600">
            Explore our diverse categories and find the perfect coloring page
            for your creative mood.
          </p>
        </div>
      </div>

      <div
        class="card bg-base-100 border border-base-300 shadow-sm hover:shadow-md transition-shadow">
        <div class="card-body items-center text-center">
          <div
            class="w-16 h-16 rounded-full bg-primary-100 flex items-center justify-center mb-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-8 w-8 text-primary-600"
              viewBox="0 0 20 20"
              fill="currentColor">
              <path
                fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                clip-rule="evenodd"></path>
            </svg>
          </div>
          <h3 class="text-xl font-bold mb-2">2. Color Online</h3>
          <p class="text-neutral-600">
            Use our interactive coloring tool with custom palettes, adjustable
            brushes, and real-time editing.
          </p>
        </div>
      </div>

      <div
        class="card bg-base-100 border border-base-300 shadow-sm hover:shadow-md transition-shadow">
        <div class="card-body items-center text-center">
          <div
            class="w-16 h-16 rounded-full bg-primary-100 flex items-center justify-center mb-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-8 w-8 text-primary-600"
              viewBox="0 0 20 20"
              fill="currentColor">
              <path
                fill-rule="evenodd"
                d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
                clip-rule="evenodd"></path>
            </svg>
          </div>
          <h3 class="text-xl font-bold mb-2">3. Download & Share</h3>
          <p class="text-neutral-600">
            Save your colored masterpiece or the original outline, then print
            and share your creative work!
          </p>
        </div>
      </div>
    </div>
  </div>
</section>
