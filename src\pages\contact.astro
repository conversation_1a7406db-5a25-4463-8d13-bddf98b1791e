---
import Layout from "../layouts/Layout.astro";
---

<Layout
  title="Contact ColoringMix - Get in Touch with Us"
  description="Have questions, suggestions, or feedback? Contact the ColoringMix team and we'll be happy to help with all your coloring page needs.">
  <div class="container mx-auto px-4 py-12 max-w-4xl">
    <h1 class="text-4xl font-bold text-primary mb-8 text-center">Contact Us</h1>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-12">
      <div class="bg-base-200 p-8 rounded-lg shadow-md">
        <h2 class="text-2xl font-bold text-secondary mb-6">
          Send Us a Message
        </h2>
        <form
          id="contact-form"
          class="space-y-6"
          action="https://formspree.io/f/YOUR_FORM_ID"
          method="POST">
          <div class="form-control w-full">
            <label class="label" for="name">
              <span class="label-text font-medium">Your Name</span>
            </label>
            <input
              type="text"
              id="name"
              name="name"
              placeholder="Enter your name"
              class="input input-bordered w-full"
              required
            />
          </div>

          <div class="form-control w-full">
            <label class="label" for="email">
              <span class="label-text font-medium">Email Address</span>
            </label>
            <input
              type="email"
              id="email"
              name="email"
              placeholder="Enter your email"
              class="input input-bordered w-full"
              required
            />
          </div>

          <div class="form-control w-full">
            <label class="label" for="subject">
              <span class="label-text font-medium">Subject</span>
            </label>
            <select
              id="subject"
              name="subject"
              class="select select-bordered w-full"
              required>
              <option value="" disabled selected>What's this about?</option>
              <option value="general">General Question</option>
              <option value="suggestion">Page Suggestion</option>
              <option value="feedback">Feedback</option>
              <option value="technical">Technical Issue</option>
              <option value="other">Other</option>
            </select>
          </div>

          <div class="form-control w-full">
            <label class="label" for="message">
              <span class="label-text font-medium">Message</span>
            </label>
            <textarea
              id="message"
              name="message"
              placeholder="Type your message here..."
              class="textarea textarea-bordered w-full h-40"
              required></textarea>
          </div>

          <button type="submit" class="btn btn-primary w-full"
            >Send Message</button
          >
        </form>
      </div>

      <div class="flex flex-col space-y-8">
        <div class="bg-base-200 p-8 rounded-lg shadow-md">
          <h2 class="text-2xl font-bold text-secondary mb-6">
            Contact Information
          </h2>
          <div class="space-y-4">
            <div>
              <h3 class="font-bold">Email</h3>
              <p>
                <a
                  href="mailto:<EMAIL>"
                  class="text-primary hover:underline"><EMAIL></a
                >
              </p>
            </div>

            <div>
              <h3 class="font-bold">Response Time</h3>
              <p>
                We aim to respond to all inquiries within 1-2 business days.
              </p>
            </div>
          </div>
        </div>

        <div class="bg-base-200 p-8 rounded-lg shadow-md">
          <h2 class="text-2xl font-bold text-secondary mb-6">
            Frequently Asked Questions
          </h2>
          <div class="space-y-4">
            <div>
              <h3 class="font-bold">Can I request a specific coloring page?</h3>
              <p>
                Yes! We welcome suggestions. Use our contact form and select
                "Page Suggestion" as the subject.
              </p>
            </div>

            <div>
              <h3 class="font-bold">Are all coloring pages free to use?</h3>
              <p>
                Yes, all our coloring pages are free for personal use. See our <a
                  href="/terms"
                  class="text-primary hover:underline">Terms of Service</a
                > for details.
              </p>
            </div>

            <div>
              <h3 class="font-bold">
                Can I use your coloring pages for my classroom?
              </h3>
              <p>
                Absolutely! We're happy to help educators. Feel free to use our
                resources in your classroom.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</Layout>

<script>
  // Simple form validation - you'll want to replace this with real form processing
  document.addEventListener("DOMContentLoaded", () => {
    const form = document.getElementById("contact-form") as HTMLFormElement;

    form?.addEventListener("submit", (e) => {
      e.preventDefault();

      // In a real implementation, you'd send the form data to your backend
      // For now, we'll just show an alert
      alert(
        "Thanks for your message! This is a demo form - in a real site, your message would be sent."
      );

      // Optional: Reset the form
      form.reset();
    });
  });
</script>
