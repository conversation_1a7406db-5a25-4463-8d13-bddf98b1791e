---
import ColoringPageCard from "../common/ColoringPageCard.astro";
import {
  getFeaturedColoringPages,
  getDisplayName,
  getColoringPageThumbnailPath,
  getColoringPageUri,
} from "../../utils/collections";

const coloringPages = await getFeaturedColoringPages();
---

<section class="py-12 bg-base-200">
  <div class="container mx-auto px-4">
    <div class="text-center mb-10">
      <h2 class="text-3xl md:text-4xl font-bold mb-2">
        Featured Coloring Pages
      </h2>
      <p class="text-neutral-600 max-w-2xl mx-auto">
        Discover our most popular coloring pages. Download, print, and start
        coloring right away!
      </p>
    </div>

    <div
      class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
      {
        coloringPages.map((page, idx) => (
          <div
            class="animate-slide-up"
            style={`animation-delay: ${idx * 100}ms`}>
            <ColoringPageCard
              id={page.slug}
              title={getDisplayName(page)}
              description={page.data.description}
              thumbnail={getColoringPageThumbnailPath(page)}
              categoryId={page.slug.split("/")[0]}
              subcategoryId={page.slug.split("/")[1]}
              difficulty={page.data.difficulty}
              slug={page.slug.split("/").pop() || ""}
              url={getColoringPageUri(page)}
            />
          </div>
        ))
      }
    </div>
    <!--
    <div class="text-center mt-10">
      <a href="/popular/" class="btn btn-primary">
        View More Popular Pages
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 ml-1"
          viewBox="0 0 20 20"
          fill="currentColor">
          <path
            fill-rule="evenodd"
            d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
            clip-rule="evenodd"></path>
        </svg>
      </a>
    </div>
    -->
  </div>
</section>
